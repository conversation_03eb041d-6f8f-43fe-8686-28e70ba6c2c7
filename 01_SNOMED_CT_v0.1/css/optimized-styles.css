/* 
 * SNOMED CT网站优化样式
 * 专注于改进工作流程图表、步骤卡片和实现方法板块的视觉效果
 */

/* SNOMED CT 优势部分样式 */
.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.advantage-item {
    background: white;
    border-radius: 12px;
    padding: 1.8rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.advantage-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.advantage-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.advantage-item:hover::before {
    opacity: 1;
}

.advantage-item h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.advantage-item h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.advantage-item:hover h3::after {
    width: 60px;
}

.advantage-item p {
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    position: relative;
    z-index: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .advantages-grid {
        grid-template-columns: 1fr;
    }
    
    .advantage-item {
        padding: 1.5rem;
    }
}

/* 工作流程图表优化 */
.workflow-diagram {
    position: relative;
    margin: 3rem 0;
    padding: 1rem 0;
}

.workflow-steps {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    position: relative;
}

.workflow-steps:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 2rem;
    width: 3px;
    background: linear-gradient(to bottom, var(--primary-light), var(--secondary-light));
    border-radius: 3px;
    z-index: 0;
}

.workflow-steps > li {
    position: relative;
    padding-left: 5rem;
    transition: transform 0.3s ease;
}

.workflow-steps > li:before {
    content: '';
    position: absolute;
    left: 1.25rem;
    top: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
    z-index: 1;
    box-shadow: 0 0 0 5px rgba(44, 109, 172, 0.2);
    transition: all 0.3s ease;
}

.workflow-steps > li:hover {
    transform: translateX(5px);
}

.workflow-steps > li:hover:before {
    background-color: var(--accent-color);
    box-shadow: 0 0 0 8px rgba(230, 126, 34, 0.2);
}

.workflow-step-content {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.workflow-steps > li:hover .workflow-step-content {
    border-left-color: var(--accent-color);
    box-shadow: var(--shadow-lg);
}

@media (min-width: 768px) {
    .workflow-steps {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .workflow-steps:before {
        left: 0;
        right: 0;
        top: 2rem;
        bottom: auto;
        width: auto;
        height: 3px;
    }
    
    .workflow-steps > li {
        flex: 0 0 calc(50% - 2rem);
        padding-left: 0;
        padding-top: 3rem;
        margin-bottom: 2rem;
    }
    
    .workflow-steps > li:before {
        left: calc(50% - 0.75rem);
        top: 0;
    }
    
    .workflow-steps > li:hover {
        transform: translateY(-5px);
    }
}

/* 工作流程步骤内容优化 */
.workflow-step-content h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
    position: relative;
    display: inline-block;
}

.workflow-step-content h3:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 2px;
}

.workflow-step-content p {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.workflow-step-content ul {
    margin-left: 1rem;
    margin-bottom: 0;
}

.workflow-step-content ul li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.workflow-step-content ul li:before {
    content: '•';
    position: absolute;
    left: 0;
    top: 0;
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.workflow-step-content ul li:hover {
    color: var(--primary-dark);
}

/* 步骤卡片优化 */
.step-card {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
    border-bottom: 3px solid transparent;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-bottom-color: var(--primary-color);
}

.step-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    border-radius: 0 0 0 var(--radius-sm);
}

.step-number {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--primary-light);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: var(--shadow-sm);
    z-index: 1;
}

.step-card h3 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
    padding-right: 3rem;
}

.step-card ul {
    margin-left: 0;
    list-style: none;
}

.step-card ul li {
    position: relative;
    padding-left: 1.8rem;
    margin-bottom: 0.8rem;
    line-height: 1.5;
}

.step-card ul li:before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 0;
    color: var(--success-color);
    font-weight: bold;
}

/* 实现方法板块优化 */
.content-block#implementation {
    background-color: var(--bg-light);
    border-radius: var(--radius-lg);
    padding: 3rem;
    margin: 3rem 0;
    position: relative;
}

.content-block#implementation:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-sm) var(--radius-sm) 0 0;
}

.content-block#implementation h2 {
    text-align: center;
    color: var(--primary-dark);
    margin-bottom: 2rem;
    font-size: 2rem;
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
    width: 100%;
}

.content-block#implementation h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25%;
    width: 50%;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 2px;
}

.process-steps {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 768px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .process-steps {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 添加一些额外的动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.workflow-steps > li,
.step-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.workflow-steps > li:nth-child(1),
.step-card:nth-child(1) {
    animation-delay: 0.1s;
}

.workflow-steps > li:nth-child(2),
.step-card:nth-child(2) {
    animation-delay: 0.2s;
}

.workflow-steps > li:nth-child(3),
.step-card:nth-child(3) {
    animation-delay: 0.3s;
}

.workflow-steps > li:nth-child(4),
.step-card:nth-child(4) {
    animation-delay: 0.4s;
}

.workflow-steps > li:nth-child(5),
.step-card:nth-child(5) {
    animation-delay: 0.5s;
}

/* 确保在页面加载后元素已经可见 */
.workflow-steps > li.animated,
.step-card.animated {
    opacity: 1;
}
