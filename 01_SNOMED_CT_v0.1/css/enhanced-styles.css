/* 
 * 增强的SNOMED CT网站样式
 * 专注于改进颜色搭配、视觉层次和响应式设计
 */

/* 全局变量优化 */
:root {
    --primary-color: #2c6dac;
    --primary-dark: #1a5085;
    --primary-light: #4a8bc7;
    --secondary-color: #36b0c9;
    --secondary-dark: #287f92;
    --secondary-light: #58d2eb;
    --accent-color: #e67e22;
    --accent-light: #f39c12;
    --text-color: #333333;
    --text-light: #666666;
    --text-lighter: #888888;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-gray: #f2f5f8;
    --bg-dark: #2c3e50;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --border-color: #e1e4e8;
    --border-light: #eaedf0;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --font-main: 'Noto Sans SC', sans-serif;
    --font-mono: 'Courier New', monospace;
    --header-height: 70px;
    --content-width: 1200px;
}

/* 基础样式增强 */
body {
    font-family: var(--font-main);
    color: var(--text-color);
    line-height: 1.7;
    background-color: var(--bg-light);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-light);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1.2rem;
}

/* 布局优化 */
.container {
    width: 100%;
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 70px 0;
}

/* 导航栏优化 */
header {
    background-color: var(--bg-white);
    box-shadow: var(--shadow-md);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition-normal);
    height: var(--header-height);
}

header.scrolled {
    box-shadow: var(--shadow-lg);
    height: 60px;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo {
    display: flex;
    flex-direction: column;
    transition: var(--transition-normal);
}

.logo h1 {
    font-size: 1.8rem;
    margin-bottom: 0;
    color: var(--primary-color);
    font-weight: 700;
    text-shadow: 0 1px 1px rgba(0,0,0,0.05);
    transition: var(--transition-fast);
}

.logo p {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-bottom: 0;
    transition: var(--transition-fast);
}

.nav-links {
    display: flex;
    gap: 10px;
}

.nav-links li {
    position: relative;
}

.nav-links a {
    color: var(--text-color);
    font-weight: 500;
    padding: 10px 15px;
    display: block;
    position: relative;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.nav-links a:hover {
    color: var(--primary-color);
    background-color: rgba(44, 109, 172, 0.05);
}

.nav-links a.active {
    color: var(--primary-color);
    background-color: rgba(44, 109, 172, 0.08);
    font-weight: 600;
}

.nav-links a.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15%;
    width: 70%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 10px 10px 0 0;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    z-index: 1010;
}

.mobile-menu-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 5px 0;
    border-radius: 3px;
    transition: var(--transition-fast);
}

/* 页面头部美化 */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    text-align: center;
    padding: 130px 0 80px;
    position: relative;
    margin-bottom: 50px;
    overflow: hidden;
}

.page-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjZmZmIiBmaWxsLW9wYWNpdHk9IjAuMDUiPjwvcmVjdD4KPC9zdmc+');
    opacity: 0.4;
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    color: white;
    font-size: 2.8rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 15px;
    font-weight: 700;
}

.page-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    font-weight: 300;
}

/* 两栏布局优化 */
.two-column-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
    margin: 40px auto;
    max-width: var(--content-width);
    padding: 0 20px;
}

/* 侧边导航优化 */
.page-nav {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: 25px;
    position: sticky;
    top: calc(var(--header-height) + 20px);
    max-height: calc(100vh - var(--header-height) - 40px);
    overflow-y: auto;
    transition: var(--transition-normal);
}

.page-nav h3 {
    color: var(--primary-dark);
    margin-bottom: 20px;
    font-size: 1.2rem;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--primary-light);
    font-weight: 600;
}

.page-nav ul {
    list-style: none;
    padding: 0;
}

.page-nav li {
    margin-bottom: 8px;
}

.page-nav a {
    display: block;
    padding: 10px 15px;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.95rem;
    position: relative;
    padding-left: 30px;
}

.page-nav a:before {
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--primary-light);
    opacity: 0.5;
    transition: var(--transition-fast);
}

.page-nav a:hover {
    background-color: rgba(44, 109, 172, 0.05);
    color: var(--primary-color);
    transform: translateX(3px);
}

.page-nav a:hover:before {
    opacity: 1;
    background-color: var(--primary-color);
}

.page-nav a.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.page-nav a.active:before {
    background-color: white;
    opacity: 1;
}

/* 内容区域美化 */
.content-section {
    padding: 0 0 60px;
}

.content-section:last-child {
    padding-bottom: 100px;
}

.content-block {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: 35px;
    margin-bottom: 40px;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.content-block:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.content-block h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-light);
    position: relative;
}

.content-block h2:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* 卡片组样式优化 */
.component-cards, .application-grid, .benefits-grid, .standards-integration, .elements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.component-card, .application-card, .benefit-item, .standard-box, .element-card {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    border-top: 4px solid transparent;
}

.component-card:hover, .application-card:hover, .benefit-item:hover, .standard-box:hover, .element-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-md);
    background-color: var(--bg-white);
}

.component-card {
    border-top-color: var(--primary-color);
}

.application-card {
    border-top-color: var(--secondary-color);
}

.benefit-item {
    border-top-color: var(--accent-color);
}

.standard-box {
    border-top-color: var(--success-color);
}

.element-card {
    border-top-color: var(--primary-light);
}

.component-card h3, .application-card h3, .benefit-item h3, .standard-box h3, .element-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--primary-dark);
    position: relative;
    padding-bottom: 10px;
}

.component-card h3:after, .application-card h3:after, .benefit-item h3:after, .standard-box h3:after, .element-card h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: currentColor;
    border-radius: 3px;
}

/* 信息框样式优化 */
.info-box {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px 30px;
    margin: 30px 0;
    box-shadow: var(--shadow-sm);
    border-left: 5px solid var(--primary-color);
    transition: var(--transition-normal);
}

.info-box:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.info-box.warning {
    border-left-color: var(--warning-color);
}

.info-box.danger {
    border-left-color: var(--error-color);
}

.info-box.success {
    border-left-color: var(--success-color);
}

.info-box h3 {
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.info-box ul {
    margin-left: 20px;
}

.info-box li {
    margin-bottom: 8px;
}

/* 步骤卡片优化 */
.process-steps {
    margin: 40px 0;
    position: relative;
}

.process-steps:before {
    content: '';
    position: absolute;
    top: 15px;
    bottom: 15px;
    left: 38px;
    width: 4px;
    background-color: var(--border-light);
    border-radius: 2px;
    z-index: 0;
}

.step-card {
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px 25px 25px 90px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.step-card:last-child {
    margin-bottom: 0;
}

.step-card:hover {
    transform: translateY(-5px) translateX(5px);
    background-color: var(--bg-white);
    box-shadow: var(--shadow-md);
}

.step-number {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(44, 109, 172, 0.3);
    z-index: 2;
}

.step-card h3 {
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

/* 比较盒子优化 */
.comparison-box {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.comparison-item {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.comparison-item:first-child {
    border-left: 5px solid var(--text-lighter);
}

.comparison-item:last-child {
    border-left: 5px solid var(--primary-color);
    background-color: rgba(44, 109, 172, 0.05);
}

.comparison-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.comparison-item h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px dashed rgba(0,0,0,0.05);
}

.comparison-item:last-child h3 {
    color: var(--primary-color);
}

/* 层次结构树优化 */
.hierarchy-container {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 30px;
    margin: 30px 0;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.hierarchy-container:hover {
    box-shadow: var(--shadow-md);
}

.hierarchy-tree ul {
    border-left: 2px solid rgba(44, 109, 172, 0.2);
    padding-left: 25px;
    margin-left: 10px;
}

.tree-item {
    display: inline-block;
    padding: 6px 15px;
    background-color: rgba(44, 109, 172, 0.1);
    border-radius: 20px;
    color: var(--primary-color);
    font-weight: 500;
    margin: 5px 0;
    transition: var(--transition-fast);
    font-size: 0.95rem;
}

.tree-item:hover {
    background-color: rgba(44, 109, 172, 0.2);
    transform: translateX(5px);
}

/* 代码示例优化 */
.coding-example {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px;
    margin: 30px 0;
    box-shadow: var(--shadow-sm);
    border-left: 5px solid var(--primary-color);
    transition: var(--transition-normal);
}

.coding-example:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.coding-example h3 {
    color: var(--primary-dark);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.coding-example pre {
    background-color: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: var(--radius-sm);
    overflow-x: auto;
    font-family: var(--font-mono);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* 图片容器优化 */
.image-box {
    margin: 30px 0;
    text-align: center;
    transition: var(--transition-normal);
}

.image-box:hover {
    transform: translateY(-5px);
}

.content-image {
    max-width: 100%;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.image-box:hover .content-image {
    box-shadow: var(--shadow-lg);
}

.image-caption {
    margin-top: 15px;
    color: var(--text-light);
    font-style: italic;
    font-size: 0.95rem;
}

/* 表格优化 */
.comparison-table {
    margin: 30px 0;
    overflow-x: auto;
}

.comparison-table table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.comparison-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    text-align: left;
    padding: 15px;
    font-size: 1rem;
}

.comparison-table th:first-child {
    width: 20%;
}

.comparison-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.comparison-table tr:last-child td {
    border-bottom: none;
}

.comparison-table tr:nth-child(even) {
    background-color: var(--bg-light);
}

.comparison-table tr:hover {
    background-color: rgba(44, 109, 172, 0.05);
}

/* 交互式案例页面特定样式 */
.demo-tabs {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin: 30px 0;
}

.tab-buttons {
    display: flex;
    background-color: var(--primary-dark);
    overflow-x: auto;
    white-space: nowrap;
}

.tab-button {
    background: none;
    border: none;
    padding: 15px 25px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.95rem;
}

.tab-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.tab-button.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.case-intro {
    margin-bottom: 30px;
}

.report-demo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.original-report, .structured-report {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px;
    box-shadow: var(--shadow-sm);
}

.original-report h4, .structured-report h4 {
    margin-bottom: 20px;
    color: var(--primary-dark);
    font-size: 1.2rem;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--border-light);
}

.report-text {
    font-size: 0.95rem;
    line-height: 1.7;
}

.report-structure {
    font-size: 0.95rem;
}

.structure-section {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--border-color);
}

.structure-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.structure-section h5 {
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--primary-dark);
}

.structure-item {
    display: flex;
    margin-bottom: 10px;
    align-items: flex-start;
}

.item-label {
    font-weight: 600;
    width: 120px;
    color: var(--text-color);
    flex-shrink: 0;
}

.item-value {
    flex: 1;
}

.snomedTerm {
    color: var(--primary-color);
    cursor: pointer;
    position: relative;
    transition: var(--transition-fast);
    display: inline-block;
    border-bottom: 1px dashed var(--primary-light);
}

.snomedTerm:hover {
    color: var(--primary-dark);
    background-color: rgba(44, 109, 172, 0.1);
    border-radius: 3px;
}

.code-view {
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 25px;
    box-shadow: var(--shadow-sm);
}

.code-view h4 {
    margin-bottom: 20px;
    color: var(--primary-dark);
    font-size: 1.2rem;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--border-light);
}

.concept-info {
    background-color: var(--bg-white);
    border-radius: var(--radius-sm);
    padding: 20px;
    box-shadow: var(--shadow-sm);
    animation: fadeIn 0.5s ease;
}

.concept-basic {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-light);
}

.concept-id, .concept-term {
    margin-bottom: 10px;
}

.concept-id span, .concept-term span {
    font-weight: 600;
    color: var(--primary-dark);
}

.concept-relations h5 {
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: var(--primary-dark);
}

.concept-relations ul {
    margin-left: 20px;
}

.concept-relations li {
    margin-bottom: 8px;
}

.hidden {
    display: none;
}

.code-prompt {
    color: var(--text-light);
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* 工作流演示优化 */
.workflow-demo {
    margin: 30px 0;
}

.workflow-steps {
    display: flex;
    margin-bottom: 30px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    cursor: pointer;
}

.workflow-step:not(:last-child):after {
    content: '';
    position: absolute;
    top: 25px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: var(--border-color);
    z-index: 0;
}

.workflow-step.active:not(:last-child):after {
    background: linear-gradient(to right, var(--primary-color), var(--border-color));
}

.workflow-step.completed:not(:last-child):after {
    background-color: var(--primary-color);
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--bg-light);
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--text-light);
    position: relative;
    z-index: 1;
    transition: var(--transition-normal);
}

.workflow-step.active .step-number {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(44, 109, 172, 0.2);
}

.workflow-step.completed .step-number {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.step-name {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
    text-align: center;
    transition: var(--transition-fast);
}

.workflow-step.active .step-name {
    color: var(--primary-color);
    font-weight: 600;
}

.workflow-content {
    background-color: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.workflow-screen {
    display: none;
    padding: 30px;
}

.workflow-screen.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.workflow-screen h4 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: var(--primary-dark);
}

.workflow-description {
    margin-bottom: 30px;
}

.workflow-image {
    text-align: center;
}

.workflow-img {
    max-width: 100%;
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.workflow-img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
}

/* 响应式优化 */
@media (max-width: 1100px) {
    .two-column-layout {
        grid-template-columns: 1fr;
    }
    
    .page-nav {
        position: relative;
        top: 0;
        margin-bottom: 30px;
        max-height: none;
    }
}

@media (max-width: 992px) {
    h1 {
        font-size: 2.2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    .page-header {
        padding: 100px 0 60px;
    }
    
    .page-header h1 {
        font-size: 2.4rem;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav-links {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        max-width: 300px;
        height: 100vh;
        background-color: var(--bg-white);
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        gap: 0;
        transition: right 0.3s ease;
        z-index: 1000;
        padding: 80px 0 30px;
        overflow-y: auto;
    }
    
    .nav-links.active {
        right: 0;
    }
    
    .nav-links li {
        width: 100%;
    }
    
    .nav-links a {
        padding: 15px 25px;
        width: 100%;
        border-radius: 0;
    }
    
    .nav-links a.active:after {
        display: none;
    }
    
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.6rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .content-block {
        padding: 25px 20px;
    }
    
    .comparison-box, .component-cards, .application-grid, .benefits-grid, .standards-integration, .elements-grid {
        grid-template-columns: 1fr;
    }
    
    .report-demo {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .logo p {
        font-size: 0.8rem;
    }
    
    .page-header {
        padding: 90px 0 50px;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
    }
    
    .page-header p {
        font-size: 1rem;
    }
    
    .structure-item {
        flex-direction: column;
    }
    
    .item-label {
        width: 100%;
        margin-bottom: 5px;
    }
}