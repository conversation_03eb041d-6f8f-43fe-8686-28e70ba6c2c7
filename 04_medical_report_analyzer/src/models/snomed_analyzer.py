import requests
import json
import os
from typing import Dict, List, Tuple, Any

class SnomedAnalyzer:
    """
    使用大模型能力对医学影像报告进行SNOMED CT术语解析的类
    """
    
    def __init__(self, api_key=None):
        """
        初始化SNOMED CT分析器
        
        Args:
            api_key: 大模型API密钥（可选，如果使用环境变量则不需要）
        """
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY", "********************************************************************************************************************************************************************")
        self.api_url = "https://api.openai.com/v1/chat/completions"
        self.model = "gpt-4"
        
    def analyze_text(self, text: str, section: str) -> Dict[str, Any]:
        """
        分析医学影像报告文本，提取SNOMED CT实体和关系
        
        Args:
            text: 需要分析的文本内容
            section: 文本所属部分，'findings'（影像所见）或'impression'（诊断意见）
            
        Returns:
            包含实体和关系的字典
        """
        # 构建提示词，指导大模型进行SNOMED CT术语解析
        prompt = self._build_prompt(text, section)
        
        # 调用大模型API
        response = self._call_llm_api(prompt)
        
        # 解析大模型返回的结果
        structured_data = self._parse_llm_response(response)
        
        return structured_data
    
    def _build_prompt(self, text: str, section: str) -> str:
        """
        构建提示词，指导大模型进行SNOMED CT术语解析
        
        Args:
            text: 需要分析的文本内容
            section: 文本所属部分
            
        Returns:
            构建好的提示词
        """
        section_name = "影像所见" if section == "findings" else "诊断意见"
        
        prompt = f"""
        你是一个专业的医学影像报告分析助手，精通SNOMED CT术语体系。请分析以下医学影像报告的{section_name}部分，
        提取其中的医学实体及其关系，并尽可能映射到SNOMED CT术语和编码。

        {section_name}文本:
        {text}

        请严格按照以下要求进行分析：
        1. 只分析提供的文本内容，不要添加任何不在原文中的实体
        2. 重点关注解剖部位（如肺、肝脏、脑等）、病理发现（如结节、肿块、积液等）、影像学表现（如密度、边缘特征、信号强度等）
        3. 对于不同影像类型，注意其特有表现：
           - CT影像：关注密度（HU值）、增强特点、钙化等
           - MRI影像：关注T1/T2信号、增强方式等
           - 超声影像：关注回声、血流信号等
           - X线影像：关注透明度、密度等
        4. 准确识别实体之间的关系，如"位于"、"表现为"、"大小为"等
        5. 尽可能提供准确的SNOMED CT编码，如无法确定可填null

        请按照以下JSON格式返回分析结果:
        {{
            "entities": [
                {{
                    "id": "E1",
                    "text": "原文中的实体文本",
                    "entity_type": "实体类型，如解剖部位、病理发现、疾病等",
                    "snomed_code": "对应的SNOMED CT编码，如未找到可为null",
                    "snomed_term": "对应的SNOMED CT标准术语，如未找到可为null",
                    "start_pos": 实体在原文中的起始位置,
                    "end_pos": 实体在原文中的结束位置
                }}
            ],
            "relationships": [
                {{
                    "source_id": "源实体ID，如E1",
                    "target_id": "目标实体ID，如E2",
                    "relation_type": "关系类型，如位于、表现为、导致等",
                    "snomed_relation_code": "对应的SNOMED CT关系编码，如未找到可为null"
                }}
            ]
        }}

        请确保分析全面、准确，尽可能多地识别医学实体和它们之间的关系。
        """
        
        return prompt
    
    def _call_llm_api(self, prompt: str) -> str:
        """
        调用大模型API
        
        Args:
            prompt: 提示词
            
        Returns:
            大模型返回的文本
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": "You are a medical report analyzer specializing in SNOMED CT."},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1
            }
            
            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()
            
            return response.json()["choices"][0]["message"]["content"]
            
        except Exception as e:
            print(f"API调用错误: {str(e)}")
            # 出错时使用备用方案
            return self._generate_fallback_response()
    
    def _generate_fallback_response(self) -> str:
        """
        当API调用失败时生成备用响应
        
        Returns:
            备用响应
        """
        return json.dumps({
            "entities": [],
            "relationships": []
        }, ensure_ascii=False)
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """
        解析大模型返回的结果
        
        Args:
            response: 大模型返回的文本
            
        Returns:
            解析后的结构化数据
        """
        try:
            # 清理响应文本，确保它是有效的JSON
            cleaned_response = response.strip()
            # 如果响应被markdown代码块包围，则去除这些标记
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            
            cleaned_response = cleaned_response.strip()
            
            # 解析JSON
            data = json.loads(cleaned_response)
            
            # 验证数据结构
            if "entities" not in data or "relationships" not in data:
                raise ValueError("响应缺少必要的字段")
                
            return data
            
        except Exception as e:
            print(f"解析响应错误: {str(e)}")
            # 返回空结构
            return {"entities": [], "relationships": []}
