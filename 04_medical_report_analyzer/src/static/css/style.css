/* 全局样式 */
:root {
    --primary-color: #3f51b5;
    --secondary-color: #f50057;
    --light-color: #f5f5f5;
    --dark-color: #333;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background-color: #f8f9fa;
    color: var(--dark-color);
}

/* 导航栏样式 */
.navbar-dark.bg-primary {
    background-color: var(--primary-color) !important;
}

.navbar-brand {
    font-weight: 600;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #303f9f;
    border-color: #303f9f;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: rgba(0, 0, 0, 0.03);
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(63, 81, 181, 0.05);
}

/* 标签页样式 */
.nav-tabs .nav-link {
    color: var(--dark-color);
    border: none;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    background-color: #fff;
}

/* 图表容器样式 */
#graph-container {
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    background-color: #fff;
}

/* 加载动画样式 */
.spinner-border {
    color: var(--primary-color);
}

/* 实体标签样式 */
.entity-tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.25rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.entity-anatomy {
    background-color: rgba(63, 81, 181, 0.1);
    color: #3f51b5;
}

.entity-finding {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.entity-disease {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.entity-severity {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        margin-bottom: 1rem;
    }
}

/* 高亮显示原文中的实体 */
.highlighted-text {
    position: relative;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    background-color: #f8f9fa;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.entity-highlight {
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
    display: inline;
}

.entity-highlight[data-type="解剖部位"] {
    background-color: rgba(63, 81, 181, 0.2);
    border-bottom: 1px solid #3f51b5;
}

.entity-highlight[data-type="病理发现"] {
    background-color: rgba(244, 67, 54, 0.2);
    border-bottom: 1px solid #f44336;
}

.entity-highlight[data-type="疾病"] {
    background-color: rgba(76, 175, 80, 0.2);
    border-bottom: 1px solid #4caf50;
}

.entity-highlight[data-type="严重程度"] {
    background-color: rgba(255, 152, 0, 0.2);
    border-bottom: 1px solid #ff9800;
}

.entity-highlight[data-type="影像学表现"] {
    background-color: rgba(156, 39, 176, 0.2);
    border-bottom: 1px solid #9c27b0;
}

/* 工具提示样式 */
.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    font-family: inherit;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: 0.875rem;
    word-wrap: break-word;
    opacity: 0;
}

.tooltip.show {
    opacity: 0.9;
}

.tooltip .tooltip-inner {
    max-width: 200px;
    padding: 0.25rem 0.5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 0.25rem;
}

/* 关系图样式 */
.node circle {
    fill: #fff;
    stroke: var(--primary-color);
    stroke-width: 2px;
}

.node text {
    font-size: 12px;
    font-family: sans-serif;
}

.link {
    fill: none;
    stroke: #ccc;
    stroke-width: 1.5px;
}

.link-label {
    font-size: 10px;
    fill: #666;
}

/* 按钮样式 */
.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-view {
    color: var(--primary-color);
    background-color: rgba(63, 81, 181, 0.1);
    border-color: transparent;
}

.btn-view:hover {
    background-color: rgba(63, 81, 181, 0.2);
    color: var(--primary-color);
}
