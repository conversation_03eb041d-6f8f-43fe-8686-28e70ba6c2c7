/**
 * SNOMED CT 医学影像报告分析系统 - 前端 JavaScript
 */

const API_BASE = '';  // 相对路径，因为前端和后端在同一域名下

// 切换标签页
function switchTab(tabName) {
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 移除所有标签的激活状态
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的标签内容
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // 激活选中的标签
    event.target.classList.add('active');
    
    // 隐藏结果
    hideResults();
}

// 显示加载状态
function showLoading() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('results').style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// 显示结果
function showResults(content) {
    document.getElementById('results-content').innerHTML = content;
    document.getElementById('results').style.display = 'block';
    hideLoading();
}

// 隐藏结果
function hideResults() {
    document.getElementById('results').style.display = 'none';
}

// 显示错误信息
function showError(message) {
    const errorHtml = `
        <div class="error">
            <strong>❌ 错误:</strong> ${message}
        </div>
    `;
    showResults(errorHtml);
}

// 显示成功信息
function showSuccess(message) {
    const successHtml = `
        <div class="success">
            <strong>✅ 成功:</strong> ${message}
        </div>
    `;
    showResults(successHtml);
}

// 格式化 SNOMED CT 实体列表
function formatSnomedEntities(entities) {
    if (!entities || entities.length === 0) {
        return '<p>未找到 SNOMED CT 实体</p>';
    }
    
    let html = '<div class="entity-list">';
    entities.forEach(entity => {
        html += `
            <div class="entity-item">
                <div class="entity-term">${entity.term}</div>
                <div class="entity-code">代码: ${entity.code}</div>
                ${entity.translation ? `<div class="entity-translation">中文: ${entity.translation}</div>` : ''}
            </div>
        `;
    });
    html += '</div>';
    
    return html;
}

// 分析完整报告
async function analyzeReport(formData) {
    try {
        const response = await fetch(`${API_BASE}/analyze/report`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                patient_info: {
                    age: formData.age,
                    sex: formData.sex
                },
                chief_complaint: formData.chief_complaint,
                examination_requested: formData.examination_requested,
                imaging_findings: formData.imaging_findings,
                diagnostic_impression: formData.diagnostic_impression
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        let resultsHtml = `
            <div class="success">
                <strong>✅ 分析完成!</strong> 
                报告ID: ${data.report_id} | 
                耗时: ${data.analysis_duration.toFixed(2)}秒 | 
                实体数量: ${data.entity_count}
            </div>
            
            <h3>📋 患者信息</h3>
            <p><strong>年龄:</strong> ${data.patient_info.age}</p>
            <p><strong>性别:</strong> ${data.patient_info.sex}</p>
            <p><strong>检查项目:</strong> ${data.examination_requested}</p>
            ${data.loinc_code ? `<p><strong>LOINC 代码:</strong> ${data.loinc_code.code} (${data.loinc_code.term})</p>` : ''}
            
            <h3>🔍 SNOMED CT 实体</h3>
            ${formatSnomedEntities(data.snomed_entities)}
            
            ${data.structured_tree_output ? `
                <h3>🌳 层次结构</h3>
                <div class="tree-output">${data.structured_tree_output}</div>
            ` : ''}
            
            ${data.gemini_analysis_summary ? `
                <h3>🤖 AI 分析摘要</h3>
                <p>${data.gemini_analysis_summary}</p>
            ` : ''}
            
            ${data.key_findings && data.key_findings.length > 0 ? `
                <h3>🎯 关键发现</h3>
                <ul>
                    ${data.key_findings.map(finding => `<li>${finding}</li>`).join('')}
                </ul>
            ` : ''}
        `;
        
        showResults(resultsHtml);
        
    } catch (error) {
        console.error('分析报告失败:', error);
        showError(`分析报告失败: ${error.message}`);
    }
}

// 分析文本
async function analyzeText(formData) {
    try {
        const response = await fetch(`${API_BASE}/analyze/text`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: formData.text,
                language: formData.language,
                analysis_type: formData.analysis_type
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        let resultsHtml = `
            <div class="success">
                <strong>✅ 文本分析完成!</strong> 
                处理时间: ${data.processing_time.toFixed(2)}秒 | 
                实体数量: ${data.snomed_entities.length}
            </div>
            
            <h3>📝 原始文本</h3>
            <p>${data.text}</p>
            
            <h3>🔍 SNOMED CT 实体</h3>
            ${formatSnomedEntities(data.snomed_entities)}
            
            ${data.hierarchy_tree ? `
                <h3>🌳 层次结构</h3>
                <div class="tree-output">${data.hierarchy_tree}</div>
            ` : ''}
            
            ${data.summary ? `
                <h3>📄 文本摘要</h3>
                <p>${data.summary}</p>
            ` : ''}
            
            ${data.key_findings && data.key_findings.length > 0 ? `
                <h3>🎯 关键发现</h3>
                <ul>
                    ${data.key_findings.map(finding => `<li>${finding}</li>`).join('')}
                </ul>
            ` : ''}
        `;
        
        showResults(resultsHtml);
        
    } catch (error) {
        console.error('文本分析失败:', error);
        showError(`文本分析失败: ${error.message}`);
    }
}

// 搜索 SNOMED CT 概念
async function searchSnomed(formData) {
    try {
        const response = await fetch(`${API_BASE}/snomed/search`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                term: formData.term,
                limit: parseInt(formData.limit)
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        let resultsHtml = `
            <div class="success">
                <strong>✅ 搜索完成!</strong> 
                搜索词: "${data.query}" | 
                找到结果: ${data.total_found}
            </div>
            
            <h3>🔍 搜索结果</h3>
            ${formatSnomedEntities(data.results)}
        `;
        
        showResults(resultsHtml);
        
    } catch (error) {
        console.error('SNOMED CT 搜索失败:', error);
        showError(`SNOMED CT 搜索失败: ${error.message}`);
    }
}

// 表单提交处理
function handleFormSubmit(formId, analyzeFunction) {
    const form = document.getElementById(formId);
    
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // 获取表单数据
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // 显示加载状态
        showLoading();
        
        // 禁用提交按钮
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        
        try {
            await analyzeFunction(data);
        } finally {
            // 重新启用提交按钮
            submitBtn.disabled = false;
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 绑定表单提交事件
    handleFormSubmit('reportForm', analyzeReport);
    handleFormSubmit('textForm', analyzeText);
    handleFormSubmit('searchForm', searchSnomed);
    
    // 添加示例数据按钮功能
    addExampleDataButtons();
});

// 添加示例数据按钮
function addExampleDataButtons() {
    // 为报告表单添加示例数据
    const reportCard = document.querySelector('#report-tab .card');
    const reportExampleBtn = document.createElement('button');
    reportExampleBtn.type = 'button';
    reportExampleBtn.className = 'btn';
    reportExampleBtn.style.marginLeft = '1rem';
    reportExampleBtn.innerHTML = '📝 填入示例数据';
    reportExampleBtn.onclick = fillReportExample;
    
    const reportSubmitBtn = document.querySelector('#reportForm button[type="submit"]');
    reportSubmitBtn.parentNode.insertBefore(reportExampleBtn, reportSubmitBtn.nextSibling);
    
    // 为文本表单添加示例数据
    const textCard = document.querySelector('#text-tab .card');
    const textExampleBtn = document.createElement('button');
    textExampleBtn.type = 'button';
    textExampleBtn.className = 'btn';
    textExampleBtn.style.marginLeft = '1rem';
    textExampleBtn.innerHTML = '📝 填入示例数据';
    textExampleBtn.onclick = fillTextExample;
    
    const textSubmitBtn = document.querySelector('#textForm button[type="submit"]');
    textSubmitBtn.parentNode.insertBefore(textExampleBtn, textSubmitBtn.nextSibling);
}

// 填入报告示例数据
function fillReportExample() {
    document.getElementById('age').value = '65';
    document.getElementById('sex').value = 'Female';
    document.getElementById('chief_complaint').value = 'Left hip pain after fall';
    document.getElementById('examination_requested').value = 'Hip X-ray';
    document.getElementById('imaging_findings').value = 'Patient presents with severe left hip pain following a fall. X-ray shows fracture of the femur neck with displacement.';
    document.getElementById('diagnostic_impression').value = 'Displaced fracture of left femur neck. Surgical intervention recommended.';
}

// 填入文本示例数据
function fillTextExample() {
    document.getElementById('text_input').value = 'Patient presents with chest pain and shortness of breath. CT scan shows pneumonia in the left lower lobe with pleural effusion.';
    document.getElementById('language').value = 'en';
    document.getElementById('analysis_type').value = 'full';
}
