<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SNOMED CT 医学影像报告分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .card h2 {
            color: #667eea;
            margin-bottom: 1rem;
            border-bottom: 2px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 5px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .results {
            display: none;
        }
        
        .entity-list {
            display: grid;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .entity-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
        }
        
        .entity-term {
            font-weight: 600;
            color: #495057;
        }
        
        .entity-code {
            color: #6c757d;
            font-family: monospace;
        }
        
        .entity-translation {
            color: #28a745;
            font-style: italic;
        }
        
        .tree-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 2rem;
        }
        
        .tab {
            padding: 1rem 2rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 SNOMED CT 医学影像报告分析系统</h1>
            <p>基于国际标准的医学术语结构化分析平台</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('report')">完整报告分析</div>
            <div class="tab" onclick="switchTab('text')">文本分析</div>
            <div class="tab" onclick="switchTab('search')">SNOMED CT 搜索</div>
        </div>
        
        <!-- 完整报告分析 -->
        <div id="report-tab" class="tab-content active">
            <div class="card">
                <h2>📋 影像报告分析</h2>
                <form id="reportForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="age">患者年龄</label>
                            <input type="text" id="age" name="age" placeholder="例如: 65" required>
                        </div>
                        <div class="form-group">
                            <label for="sex">患者性别</label>
                            <select id="sex" name="sex" required>
                                <option value="">请选择</option>
                                <option value="Male">男性</option>
                                <option value="Female">女性</option>
                                <option value="Other">其他</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="chief_complaint">主诉</label>
                        <input type="text" id="chief_complaint" name="chief_complaint" 
                               placeholder="例如: 胸痛、呼吸困难" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="examination_requested">申请检查</label>
                        <input type="text" id="examination_requested" name="examination_requested" 
                               placeholder="例如: 胸部CT、头部MRI" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="imaging_findings">影像所见</label>
                        <textarea id="imaging_findings" name="imaging_findings" 
                                  placeholder="请输入详细的影像检查所见..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="diagnostic_impression">诊断印象</label>
                        <textarea id="diagnostic_impression" name="diagnostic_impression" 
                                  placeholder="请输入诊断印象和建议..." required></textarea>
                    </div>
                    
                    <button type="submit" class="btn">🔍 开始分析</button>
                </form>
            </div>
        </div>
        
        <!-- 文本分析 -->
        <div id="text-tab" class="tab-content">
            <div class="card">
                <h2>📝 文本分析</h2>
                <form id="textForm">
                    <div class="form-group">
                        <label for="text_input">医学文本</label>
                        <textarea id="text_input" name="text" 
                                  placeholder="请输入需要分析的医学文本..." required></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="language">文本语言</label>
                            <select id="language" name="language">
                                <option value="en">英文</option>
                                <option value="zh">中文</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="analysis_type">分析类型</label>
                            <select id="analysis_type" name="analysis_type">
                                <option value="full">完整分析</option>
                                <option value="entities_only">仅提取实体</option>
                                <option value="summary_only">仅生成摘要</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">🔍 分析文本</button>
                </form>
            </div>
        </div>
        
        <!-- SNOMED CT 搜索 -->
        <div id="search-tab" class="tab-content">
            <div class="card">
                <h2>🔍 SNOMED CT 概念搜索</h2>
                <form id="searchForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="search_term">搜索词</label>
                            <input type="text" id="search_term" name="term" 
                                   placeholder="例如: pneumonia, fracture" required>
                        </div>
                        <div class="form-group">
                            <label for="limit">结果数量</label>
                            <select id="limit" name="limit">
                                <option value="5">5</option>
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">🔍 搜索概念</button>
                </form>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在分析中，请稍候...</p>
        </div>
        
        <!-- 结果显示 -->
        <div id="results" class="results">
            <div class="card">
                <h2>📊 分析结果</h2>
                <div id="results-content"></div>
            </div>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
