"""
FastAPI 数据模型定义
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class PatientInfoRequest(BaseModel):
    """患者信息请求模型"""
    age: str = Field(..., description="患者年龄")
    sex: str = Field(..., description="患者性别")


class ImagingReportRequest(BaseModel):
    """影像报告请求模型"""
    patient_info: PatientInfoRequest
    chief_complaint: str = Field(..., description="主诉")
    examination_requested: str = Field(..., description="申请检查")
    imaging_findings: str = Field(..., description="影像所见")
    diagnostic_impression: str = Field(..., description="诊断印象")


class SnomedEntityResponse(BaseModel):
    """SNOMED CT 实体响应模型"""
    term: str = Field(..., description="术语名称")
    code: str = Field(..., description="SNOMED CT 代码")
    relationships: Dict[str, Any] = Field(default_factory=dict, description="关系信息")
    translation: Optional[str] = Field(None, description="中文翻译")


class LoincTermResponse(BaseModel):
    """LOINC 术语响应模型"""
    term: str = Field(..., description="术语名称")
    code: str = Field(..., description="LOINC 代码")


class AnalysisResponse(BaseModel):
    """分析结果响应模型"""
    report_id: str = Field(..., description="报告ID")
    timestamp: datetime = Field(..., description="分析时间")
    
    # 患者和检查信息
    patient_info: PatientInfoRequest
    examination_requested: str
    loinc_code: Optional[LoincTermResponse] = None
    
    # 报告内容
    chief_complaint: str
    imaging_findings: str
    diagnostic_impression: str
    
    # SNOMED CT 分析结果
    snomed_entities: List[SnomedEntityResponse] = Field(default_factory=list)
    structured_tree_output: str = Field("", description="层次结构树输出")
    
    # AI 分析结果
    gemini_analysis_summary: str = Field("", description="Gemini 分析摘要")
    key_findings: List[str] = Field(default_factory=list, description="关键发现")
    
    # 统计信息
    entity_count: int = Field(0, description="实体数量")
    analysis_duration: float = Field(0.0, description="分析耗时（秒）")


class TextAnalysisRequest(BaseModel):
    """文本分析请求模型"""
    text: str = Field(..., description="待分析文本")
    language: str = Field("en", description="文本语言")
    analysis_type: str = Field("full", description="分析类型: full, entities_only, summary_only")


class TextAnalysisResponse(BaseModel):
    """文本分析响应模型"""
    text: str = Field(..., description="原始文本")
    language: str = Field(..., description="文本语言")
    
    # SNOMED CT 实体
    snomed_entities: List[SnomedEntityResponse] = Field(default_factory=list)
    
    # 层次结构
    hierarchy_tree: str = Field("", description="层次结构树")
    
    # AI 分析
    summary: str = Field("", description="文本摘要")
    key_findings: List[str] = Field(default_factory=list, description="关键发现")
    
    # 元数据
    analysis_timestamp: datetime = Field(default_factory=datetime.now)
    processing_time: float = Field(0.0, description="处理时间（秒）")


class HistoryResponse(BaseModel):
    """历史记录响应模型"""
    total_count: int = Field(..., description="总记录数")
    analyses: List[AnalysisResponse] = Field(..., description="分析记录列表")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = Field(..., description="版本号")
    services: Dict[str, str] = Field(default_factory=dict, description="依赖服务状态")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now)


class SnomedSearchRequest(BaseModel):
    """SNOMED CT 搜索请求模型"""
    term: str = Field(..., description="搜索词")
    limit: int = Field(20, description="返回结果数量限制")
    semantic_tags: Optional[List[str]] = Field(None, description="语义标签过滤器")


class SnomedSearchResponse(BaseModel):
    """SNOMED CT 搜索响应模型"""
    query: str = Field(..., description="搜索词")
    total_found: int = Field(..., description="找到的结果总数")
    results: List[SnomedEntityResponse] = Field(..., description="搜索结果")


class ConceptHierarchyResponse(BaseModel):
    """概念层次结构响应模型"""
    concept_id: str = Field(..., description="概念ID")
    concept_term: str = Field(..., description="概念术语")
    parents: List[SnomedEntityResponse] = Field(default_factory=list, description="父概念")
    children: List[SnomedEntityResponse] = Field(default_factory=list, description="子概念")
    ancestors: List[SnomedEntityResponse] = Field(default_factory=list, description="祖先概念")


class BatchAnalysisRequest(BaseModel):
    """批量分析请求模型"""
    reports: List[ImagingReportRequest] = Field(..., description="报告列表")
    options: Dict[str, Any] = Field(default_factory=dict, description="分析选项")


class BatchAnalysisResponse(BaseModel):
    """批量分析响应模型"""
    total_reports: int = Field(..., description="总报告数")
    successful_analyses: int = Field(..., description="成功分析数")
    failed_analyses: int = Field(..., description="失败分析数")
    results: List[AnalysisResponse] = Field(..., description="分析结果列表")
    errors: List[ErrorResponse] = Field(default_factory=list, description="错误列表")
    total_processing_time: float = Field(..., description="总处理时间（秒）")
