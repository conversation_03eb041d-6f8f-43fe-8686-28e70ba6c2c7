# SNOMED CT 医学影像报告结构化分析系统 v2.0

## 🏥 项目简介

这是一个基于 SNOMED CT 国际标准的医学影像报告结构化分析系统，能够自动提取医学文本中的标准化术语，构建概念层次关系，并提供智能分析功能。

### ✨ 主要特性

- 🔍 **真实 SNOMED CT API 集成**: 使用 Snowstorm 服务器进行实时术语查询
- 🧠 **智能 NLP 处理**: 自动识别和提取医学概念
- 🌳 **层次结构分析**: 构建完整的医学概念关系树
- 🌐 **多语言支持**: 中英文术语翻译和显示
- 🚀 **FastAPI 服务**: 高性能异步 API 服务
- 💻 **Web 界面**: 直观的前端操作界面
- 📊 **批量处理**: 支持批量分析多个报告
- 💾 **历史记录**: 完整的分析历史管理

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI 服务  │    │  SNOMED CT API  │
│   (HTML/JS)     │◄──►│   (Python)      │◄──►│   (Snowstorm)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   NLP 处理器    │
                       │  (spaCy/BERT)   │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   数据存储      │
                       │  (SQLite/JSON)  │
                       └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- 8GB+ RAM (推荐)
- 网络连接 (访问 SNOMED CT API)

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd 03_Image_snomed_struction

# 安装 Python 依赖
pip install -r requirements.txt

# 安装医学 NLP 模型
python start_server.py --install-models
```

### 3. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件（可选）
nano .env
```

### 4. 启动服务

```bash
# 启动开发服务器
python start_server.py

# 或者直接使用 uvicorn
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 5. 访问系统

- **Web 界面**: http://localhost:8000/static/index.html
- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc

## 📖 API 使用指南

### 完整报告分析

```bash
curl -X POST "http://localhost:8000/analyze/report" \
  -H "Content-Type: application/json" \
  -d '{
    "patient_info": {
      "age": "65",
      "sex": "Female"
    },
    "chief_complaint": "Left hip pain after fall",
    "examination_requested": "Hip X-ray",
    "imaging_findings": "X-ray shows fracture of the femur neck with displacement.",
    "diagnostic_impression": "Displaced fracture of left femur neck."
  }'
```

### 文本分析

```bash
curl -X POST "http://localhost:8000/analyze/text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Patient has pneumonia in left lower lobe",
    "language": "en",
    "analysis_type": "full"
  }'
```

### SNOMED CT 搜索

```bash
curl -X POST "http://localhost:8000/snomed/search" \
  -H "Content-Type: application/json" \
  -d '{
    "term": "pneumonia",
    "limit": 10
  }'
```

## 🧪 测试

### 运行 API 测试

```bash
# 确保服务器已启动
python start_server.py

# 在另一个终端运行测试
python test_api.py
```

### 运行单元测试

```bash
# 运行原有的功能测试
python test_functionality.py

# 运行 pytest 测试（如果有）
pytest tests/
```

## 📁 项目结构

```
03_Image_snomed_struction/
├── app.py                 # FastAPI 主应用
├── config.py              # 配置文件
├── snomed_client.py       # SNOMED CT API 客户端
├── api_models.py          # API 数据模型
├── models.py              # 核心数据模型
├── terminology_services.py # 术语服务
├── utils.py               # 工具函数
├── history_manager.py     # 历史记录管理
├── start_server.py        # 启动脚本
├── test_api.py           # API 测试脚本
├── test_functionality.py # 功能测试脚本
├── requirements.txt       # Python 依赖
├── .env.example          # 环境配置示例
├── static/               # 前端文件
│   ├── index.html        # 主页面
│   └── app.js           # 前端 JavaScript
├── logs/                 # 日志文件
├── uploads/              # 上传文件
└── history_records/      # 历史记录
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `SNOMED_API_BASE_URL` | SNOMED CT API 地址 | `https://snowstorm.ihtsdotools.org/snowstorm/snomed-ct` |
| `GEMINI_API_KEY` | Google Gemini API 密钥 | - |
| `OPENAI_API_KEY` | OpenAI API 密钥（备用） | - |
| `LOG_LEVEL` | 日志级别 | `INFO` |
| `PORT` | 服务端口 | `8000` |

### SNOMED CT API 配置

系统默认使用公开的 Snowstorm 服务器，也可以配置私有部署：

```env
SNOMED_API_BASE_URL=https://your-snowstorm-server.com/snowstorm/snomed-ct
SNOMED_BRANCH=MAIN/SNOMEDCT-US
SNOMED_EDITION=us-edition
```

## 🔧 高级功能

### 批量分析

```python
import httpx

async def batch_analyze():
    reports = [
        {
            "patient_info": {"age": "45", "sex": "Male"},
            "chief_complaint": "Chest pain",
            "examination_requested": "Chest CT",
            "imaging_findings": "...",
            "diagnostic_impression": "..."
        },
        # 更多报告...
    ]
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/analyze/batch",
            json={"reports": reports}
        )
        return response.json()
```

### 概念层次查询

```bash
curl "http://localhost:8000/snomed/concept/233604007/hierarchy"
```

## 🐛 故障排除

### 常见问题

1. **SNOMED CT API 连接失败**
   - 检查网络连接
   - 验证 API 地址配置
   - 查看服务器日志

2. **NLP 模型加载失败**
   - 运行 `python start_server.py --install-models`
   - 检查磁盘空间
   - 确认网络连接

3. **分析结果不准确**
   - 检查输入文本格式
   - 验证语言设置
   - 查看详细错误日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [SNOMED International](https://www.snomed.org/) - SNOMED CT 标准
- [Snowstorm](https://github.com/IHTSDO/snowstorm) - SNOMED CT 服务器
- [FastAPI](https://fastapi.tiangolo.com/) - Web 框架
- [spaCy](https://spacy.io/) - NLP 库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 项目讨论区

---

**版本**: 2.0.0  
**更新时间**: 2025年5月  
**状态**: 生产就绪 ✅
