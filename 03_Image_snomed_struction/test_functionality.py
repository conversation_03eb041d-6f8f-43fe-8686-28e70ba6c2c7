#!/usr/bin/env python3
"""
测试脚本：验证 SNOMED CT 医学影像报告结构化分析系统的核心功能
"""

from models import ImagingReport, PatientInfo, SnomedEntity, LoincTerm, ReportAnalysis
from terminology_services import extract_snomed_entities_from_text, get_loinc_code_for_examination, translate_term, analyze_text_with_gemini
from utils import build_snomed_tree
from history_manager import save_analysis, load_all_analyses
from typing import List
import io

def test_snomed_entity_extraction():
    """测试 SNOMED CT 实体提取功能"""
    print("=== 测试 SNOMED CT 实体提取功能 ===")
    
    # 测试肺炎案例
    pneumonia_text = "Pat<PERSON> has pneumonia in the left lower lobe"
    entities = extract_snomed_entities_from_text(pneumonia_text)
    print(f"肺炎文本: {pneumonia_text}")
    print(f"提取的实体数量: {len(entities)}")
    for entity in entities:
        print(f"  - {entity.term} ({entity.code})")
    
    # 测试骨折案例
    fracture_text = "X-ray shows fracture of the femur with displacement"
    entities = extract_snomed_entities_from_text(fracture_text)
    print(f"\n骨折文本: {fracture_text}")
    print(f"提取的实体数量: {len(entities)}")
    for entity in entities:
        print(f"  - {entity.term} ({entity.code})")
    
    print("✓ SNOMED CT 实体提取功能测试完成\n")

def test_snomed_tree_building():
    """测试 SNOMED CT 层次结构构建功能"""
    print("=== 测试 SNOMED CT 层次结构构建功能 ===")
    
    # 获取骨折相关的实体
    fracture_text = "Patient has fracture of femur"
    flat_entities = extract_snomed_entities_from_text(fracture_text)
    
    if flat_entities:
        print("平面实体列表:")
        for entity in flat_entities:
            print(f"  - {entity.term} ({entity.code}), 父级: {entity.relationships.get('parent_code', 'None')}")
        
        # 构建层次结构
        tree_entities = build_snomed_tree(flat_entities)
        print(f"\n构建的树结构 (根节点数量: {len(tree_entities)}):")
        for root in tree_entities:
            print(root)
    else:
        print("未找到相关实体")
    
    print("✓ SNOMED CT 层次结构构建功能测试完成\n")

def test_loinc_code_lookup():
    """测试 LOINC 代码查找功能"""
    print("=== 测试 LOINC 代码查找功能 ===")
    
    examinations = ["Chest X-ray", "MRI Brain", "CT Abdomen"]
    
    for exam in examinations:
        loinc_code = get_loinc_code_for_examination(exam)
        if loinc_code:
            print(f"检查: {exam} -> LOINC: {loinc_code.code} ({loinc_code.term})")
        else:
            print(f"检查: {exam} -> 未找到 LOINC 代码")
    
    print("✓ LOINC 代码查找功能测试完成\n")

def test_translation():
    """测试术语翻译功能"""
    print("=== 测试术语翻译功能 ===")
    
    terms = ["Fracture of femur", "Pneumonia", "Chest X-ray", "Unknown term"]
    
    for term in terms:
        translated = translate_term(term, "zh")
        print(f"{term} -> {translated}")
    
    print("✓ 术语翻译功能测试完成\n")

def test_gemini_analysis():
    """测试 Gemini 分析功能"""
    print("=== 测试 Gemini 分析功能 ===")
    
    test_text = "Patient presents with chest pain and shortness of breath. CT shows pneumonia."
    
    # 测试不同的分析类型
    summary = analyze_text_with_gemini(test_text, "summarize")
    print(f"摘要分析: {summary}")
    
    key_findings = analyze_text_with_gemini(test_text, "extract_key_findings")
    print(f"关键发现: {key_findings}")
    
    print("✓ Gemini 分析功能测试完成\n")

def test_complete_workflow():
    """测试完整的工作流程"""
    print("=== 测试完整工作流程 ===")
    
    # 创建测试报告
    patient_info = PatientInfo(age="45", sex="Male")
    report = ImagingReport(
        patient_info=patient_info,
        chief_complaint="胸痛",
        examination_requested="Chest X-ray",
        imaging_findings="Patient presents with chest pain. X-ray shows pneumonia in left lower lobe.",
        diagnostic_impression="Left lower lobe pneumonia. Antibiotic therapy recommended."
    )
    
    print(f"创建的报告: {report.timestamp}")
    
    # 提取 SNOMED 实体
    findings_entities = extract_snomed_entities_from_text(report.imaging_findings)
    impression_entities = extract_snomed_entities_from_text(report.diagnostic_impression)
    report.snomed_entities = findings_entities + impression_entities
    
    # 获取 LOINC 代码
    loinc_code = get_loinc_code_for_examination(report.examination_requested)
    if loinc_code:
        report.loinc_code = loinc_code
    
    # Gemini 分析
    gemini_summary = analyze_text_with_gemini(report.imaging_findings, "summarize")
    
    # 构建树结构
    tree_entities = build_snomed_tree(report.snomed_entities)
    tree_output = ""
    if tree_entities:
        tree_string_io = io.StringIO()
        for root in tree_entities:
            tree_string_io.write(str(root))
        tree_output = tree_string_io.getvalue()
        tree_string_io.close()
    
    # 创建分析对象
    analysis = ReportAnalysis(
        report=report,
        gemini_analysis_summary=gemini_summary,
        structured_tree_output=tree_output
    )
    
    # 保存分析
    save_analysis(analysis)
    print("分析已保存到历史记录")
    
    # 加载所有分析
    all_analyses = load_all_analyses()
    print(f"历史记录中共有 {len(all_analyses)} 个分析")
    
    print("✓ 完整工作流程测试完成\n")

def main():
    """主测试函数"""
    print("开始测试 SNOMED CT 医学影像报告结构化分析系统")
    print("=" * 60)
    
    try:
        test_snomed_entity_extraction()
        test_snomed_tree_building()
        test_loinc_code_lookup()
        test_translation()
        test_gemini_analysis()
        test_complete_workflow()
        
        print("=" * 60)
        print("✅ 所有测试完成！系统功能正常。")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
