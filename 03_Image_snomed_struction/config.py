"""
配置文件 - SNOMED CT 医学影像报告结构化分析系统
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用程序配置"""
    
    # 应用基本配置
    app_name: str = "SNOMED CT Medical Report Analyzer"
    app_version: str = "2.0.0"
    debug: bool = False
    
    # API 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # SNOMED CT API 配置
    snomed_api_base_url: str = "https://snowstorm.ihtsdotools.org/snowstorm/snomed-ct"
    snomed_api_timeout: int = 30
    snomed_branch: str = "MAIN"
    snomed_edition: str = "en-edition"
    
    # LOINC API 配置 (可选)
    loinc_api_base_url: str = "https://fhir.loinc.org"
    loinc_api_timeout: int = 30
    
    # Google Gemini API 配置
    gemini_api_key: Optional[str] = None
    gemini_model: str = "gemini-pro"
    
    # OpenAI API 配置 (备用)
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-4"
    
    # NLP 模型配置
    spacy_model: str = "en_core_sci_sm"
    biobert_model: str = "dmis-lab/biobert-base-cased-v1.2"
    
    # 数据库配置 (用于缓存)
    database_url: str = "sqlite:///./snomed_cache.db"
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 3600  # 1小时
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # 文件存储配置
    upload_dir: str = "uploads"
    history_dir: str = "history_records"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # 安全配置
    secret_key: str = "your-secret-key-change-in-production"
    access_token_expire_minutes: int = 30
    
    # CORS 配置
    allowed_origins: list = ["*"]
    allowed_methods: list = ["*"]
    allowed_headers: list = ["*"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局配置实例
settings = Settings()


# SNOMED CT 概念类型映射
SNOMED_CONCEPT_TYPES = {
    "404684003": "Clinical finding",
    "71388002": "Procedure", 
    "123037004": "Body structure",
    "260787004": "Physical object",
    "362981000": "Qualifier value",
    "410668003": "Attribute",
    "272379006": "Event",
    "105590001": "Substance",
    "78621006": "Physical force",
    "308916002": "Environment or geographical location"
}

# 医学影像检查类型到 LOINC 代码的映射
IMAGING_LOINC_MAPPING = {
    "chest x-ray": "30746-2",
    "chest xray": "30746-2", 
    "chest radiograph": "30746-2",
    "胸部x光": "30746-2",
    "胸片": "30746-2",
    
    "ct chest": "24627-2",
    "chest ct": "24627-2",
    "胸部ct": "24627-2",
    
    "mri brain": "24727-0",
    "brain mri": "24727-0", 
    "头部mri": "24727-0",
    "脑部mri": "24727-0",
    
    "ct head": "24803-9",
    "head ct": "24803-9",
    "头部ct": "24803-9",
    
    "ultrasound abdomen": "24982-1",
    "abdominal ultrasound": "24982-1",
    "腹部超声": "24982-1",
    
    "mammography": "24604-1",
    "乳腺x光": "24604-1"
}

# 常用医学术语中英文对照
MEDICAL_TERMS_TRANSLATION = {
    # 解剖结构
    "lung": "肺",
    "heart": "心脏", 
    "liver": "肝脏",
    "kidney": "肾脏",
    "brain": "大脑",
    "bone": "骨骼",
    "muscle": "肌肉",
    "blood vessel": "血管",
    
    # 病理发现
    "pneumonia": "肺炎",
    "fracture": "骨折", 
    "tumor": "肿瘤",
    "inflammation": "炎症",
    "infection": "感染",
    "hemorrhage": "出血",
    "edema": "水肿",
    "atrophy": "萎缩",
    
    # 检查方法
    "x-ray": "X光",
    "ct scan": "CT扫描",
    "mri": "磁共振成像",
    "ultrasound": "超声",
    "mammography": "乳腺X光摄影",
    
    # 位置描述
    "left": "左侧",
    "right": "右侧", 
    "bilateral": "双侧",
    "anterior": "前部",
    "posterior": "后部",
    "superior": "上部",
    "inferior": "下部",
    "medial": "内侧",
    "lateral": "外侧"
}
