#!/usr/bin/env python3
"""
API 功能测试脚本
"""

import asyncio
import httpx
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

async def test_health_check():
    """测试健康检查端点"""
    print("🔍 测试健康检查...")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data['status']}")
                print(f"   版本: {data['version']}")
                print(f"   服务状态: {data['services']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False

async def test_text_analysis():
    """测试文本分析功能"""
    print("\n🔍 测试文本分析...")
    
    test_data = {
        "text": "Patient presents with chest pain and shortness of breath. CT scan shows pneumonia in the left lower lobe with pleural effusion.",
        "language": "en",
        "analysis_type": "full"
    }
    
    async with httpx.AsyncClient(timeout=30) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/analyze/text",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 文本分析成功")
                print(f"   处理时间: {data['processing_time']:.2f}秒")
                print(f"   提取实体数: {len(data['snomed_entities'])}")
                
                if data['snomed_entities']:
                    print("   SNOMED CT 实体:")
                    for entity in data['snomed_entities'][:3]:  # 显示前3个
                        print(f"     - {entity['term']} ({entity['code']}) -> {entity['translation']}")
                
                if data['summary']:
                    print(f"   AI 摘要: {data['summary'][:100]}...")
                
                return True
            else:
                print(f"❌ 文本分析失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 文本分析异常: {e}")
            return False

async def test_report_analysis():
    """测试完整报告分析功能"""
    print("\n🔍 测试完整报告分析...")
    
    test_data = {
        "patient_info": {
            "age": "65",
            "sex": "Female"
        },
        "chief_complaint": "Left hip pain after fall",
        "examination_requested": "Hip X-ray",
        "imaging_findings": "Patient presents with severe left hip pain following a fall. X-ray shows fracture of the femur neck with displacement.",
        "diagnostic_impression": "Displaced fracture of left femur neck. Surgical intervention recommended."
    }
    
    async with httpx.AsyncClient(timeout=60) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/analyze/report",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 报告分析成功")
                print(f"   报告ID: {data['report_id']}")
                print(f"   分析时间: {data['analysis_duration']:.2f}秒")
                print(f"   实体数量: {data['entity_count']}")
                
                if data['loinc_code']:
                    print(f"   LOINC 代码: {data['loinc_code']['code']} ({data['loinc_code']['term']})")
                
                if data['snomed_entities']:
                    print("   主要 SNOMED CT 实体:")
                    for entity in data['snomed_entities'][:5]:  # 显示前5个
                        print(f"     - {entity['term']} ({entity['code']}) -> {entity['translation']}")
                
                if data['structured_tree_output']:
                    print("   层次结构:")
                    tree_lines = data['structured_tree_output'].split('\n')[:5]  # 显示前5行
                    for line in tree_lines:
                        if line.strip():
                            print(f"     {line}")
                
                return True
            else:
                print(f"❌ 报告分析失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 报告分析异常: {e}")
            return False

async def test_snomed_search():
    """测试 SNOMED CT 搜索功能"""
    print("\n🔍 测试 SNOMED CT 搜索...")
    
    test_data = {
        "term": "pneumonia",
        "limit": 5
    }
    
    async with httpx.AsyncClient(timeout=30) as client:
        try:
            response = await client.post(
                f"{BASE_URL}/snomed/search",
                json=test_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SNOMED CT 搜索成功")
                print(f"   搜索词: {data['query']}")
                print(f"   找到结果: {data['total_found']}")
                
                if data['results']:
                    print("   搜索结果:")
                    for result in data['results'][:3]:  # 显示前3个
                        print(f"     - {result['term']} ({result['code']}) -> {result['translation']}")
                
                return True
            else:
                print(f"❌ SNOMED CT 搜索失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ SNOMED CT 搜索异常: {e}")
            return False

async def test_history():
    """测试历史记录功能"""
    print("\n🔍 测试历史记录...")
    
    async with httpx.AsyncClient(timeout=30) as client:
        try:
            response = await client.get(f"{BASE_URL}/history")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 历史记录获取成功")
                print(f"   总记录数: {data['total_count']}")
                
                if data['analyses']:
                    print("   最近的分析:")
                    for analysis in data['analyses'][:2]:  # 显示前2个
                        print(f"     - 时间: {analysis['timestamp']}")
                        print(f"       患者: {analysis['patient_info']['age']}岁 {analysis['patient_info']['sex']}")
                        print(f"       检查: {analysis['examination_requested']}")
                
                return True
            else:
                print(f"❌ 历史记录获取失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 历史记录获取异常: {e}")
            return False

async def run_all_tests():
    """运行所有测试"""
    print("🧪 开始 API 功能测试")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health_check),
        ("文本分析", test_text_analysis),
        ("报告分析", test_report_analysis),
        ("SNOMED CT 搜索", test_snomed_search),
        ("历史记录", test_history)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！API 服务运行正常。")
    else:
        print("⚠️  部分测试失败，请检查服务配置。")

def main():
    """主函数"""
    print("🏥 SNOMED CT 医学影像报告分析系统 - API 测试")
    print("请确保服务器已启动 (python start_server.py)")
    print()
    
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"❌ 测试运行异常: {e}")

if __name__ == "__main__":
    main()
