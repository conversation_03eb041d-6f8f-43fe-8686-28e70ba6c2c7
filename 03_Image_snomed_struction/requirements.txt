# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# HTTP client for API calls
httpx==0.25.2
requests==2.31.0

# NLP and text processing
spacy==3.7.2
scispacy==0.5.3
transformers==4.35.2
torch==2.1.1

# Medical NLP models (will be downloaded separately)
# en_core_sci_sm
# en_ner_bc5cdr_md

# Data processing
pandas==2.1.3
numpy==1.25.2

# Configuration and environment
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Database (optional for caching)
sqlalchemy==2.0.23
sqlite3

# Logging and monitoring
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
