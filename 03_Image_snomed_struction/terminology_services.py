from models import SnomedEntity, LoincTerm
from typing import List, Optional
import asyncio
from snomed_client import nlp_processor, snomed_client
from config import IMAGING_LOINC_MAPPING, MEDICAL_TERMS_TRANSLATION
import httpx
from loguru import logger

def extract_snomed_entities_from_text(text: str, language_code: str = "en") -> List[SnomedEntity]:
    """
    从文本中提取 SNOMED CT 实体（同步版本，用于向后兼容）
    """
    try:
        # 使用异步函数的同步包装
        return asyncio.run(extract_snomed_entities_async(text, language_code))
    except Exception as e:
        logger.error(f"提取 SNOMED 实体失败: {e}")
        # 回退到模拟数据
        return _extract_snomed_entities_fallback(text, language_code)

async def extract_snomed_entities_async(text: str, language_code: str = "en") -> List[SnomedEntity]:
    """
    从文本中提取 SNOMED CT 实体（异步版本）
    """
    try:
        logger.info(f"正在分析文本: '{text[:50]}...' (语言: {language_code})")

        # 使用真实的 NLP 处理器分析文本
        entities = await nlp_processor.analyze_medical_text(text)

        if entities:
            logger.info(f"成功提取 {len(entities)} 个 SNOMED CT 实体")
            return entities
        else:
            logger.warning("未找到 SNOMED CT 实体，使用回退方案")
            return _extract_snomed_entities_fallback(text, language_code)

    except Exception as e:
        logger.error(f"SNOMED CT 实体提取失败: {e}")
        return _extract_snomed_entities_fallback(text, language_code)

def _extract_snomed_entities_fallback(text: str, language_code: str = "en") -> List[SnomedEntity]:
    """
    回退方案：使用模拟数据
    """
    logger.info(f"使用回退方案提取 SNOMED 实体: '{text[:50]}...' (语言: {language_code})")

    if "fracture" in text.lower() and "femur" in text.lower():
        return [
            SnomedEntity(term="Fracture of femur", code="71620000", relationships={}),
            SnomedEntity(term="Fracture of bone", code="125605004", relationships={'parent_code': '71620000'}),
            SnomedEntity(term="Traumatic injury", code="417163006", relationships={'parent_code': '125605004'})
        ]
    elif "pneumonia" in text.lower():
        return [SnomedEntity(term="Pneumonia", code="233604007", relationships={})]
    elif "headache" in text.lower():
        return [
            SnomedEntity(term="Headache", code="25064002", relationships={}),
            SnomedEntity(term="Pain", code="22253000", relationships={'parent_code': '25064002'})
        ]
    return []

def get_loinc_code_for_examination(examination_name: str) -> Optional[LoincTerm]:
    """
    根据检查名称获取对应的 LOINC 代码
    """
    logger.info(f"查找检查项目的 LOINC 代码: '{examination_name}'")

    # 标准化检查名称
    exam_lower = examination_name.lower().strip()

    # 在映射表中查找
    for key, loinc_code in IMAGING_LOINC_MAPPING.items():
        if key in exam_lower:
            # 这里可以扩展为真实的 LOINC API 调用
            term_name = _get_loinc_term_name(key)
            logger.info(f"找到匹配的 LOINC 代码: {loinc_code} ({term_name})")
            return LoincTerm(term=term_name, code=loinc_code)

    logger.warning(f"未找到匹配的 LOINC 代码: {examination_name}")
    return None

def _get_loinc_term_name(key: str) -> str:
    """获取 LOINC 术语的标准名称"""
    name_mapping = {
        "chest x-ray": "Chest X-ray",
        "chest xray": "Chest X-ray",
        "chest radiograph": "Chest radiograph",
        "胸部x光": "胸部X光",
        "胸片": "胸部X光片",
        "ct chest": "CT chest",
        "chest ct": "CT chest",
        "胸部ct": "胸部CT",
        "mri brain": "MRI brain",
        "brain mri": "MRI brain",
        "头部mri": "头部MRI",
        "脑部mri": "脑部MRI"
    }
    return name_mapping.get(key, key.title())

def translate_term(term: str, target_language: str = "zh") -> str:
    """
    翻译医学术语
    """
    logger.debug(f"翻译术语 '{term}' 到 {target_language}")

    if target_language == "zh":
        # 使用配置文件中的翻译映射
        term_lower = term.lower()

        # 精确匹配
        if term_lower in MEDICAL_TERMS_TRANSLATION:
            return MEDICAL_TERMS_TRANSLATION[term_lower]

        # 部分匹配
        for en_term, zh_term in MEDICAL_TERMS_TRANSLATION.items():
            if en_term in term_lower:
                return zh_term

        # 特殊医学术语处理
        special_translations = {
            "fracture of femur": "股骨骨折",
            "pneumonia": "肺炎",
            "chest x-ray": "胸部X光",
            "mri brain": "脑部MRI",
            "ct scan": "CT扫描",
            "ultrasound": "超声检查",
            "left lower lobe": "左下肺叶",
            "pleural effusion": "胸腔积液",
            "neck of femur": "股骨颈",
            "shaft of femur": "股骨干"
        }

        for en_key, zh_value in special_translations.items():
            if en_key in term_lower:
                return zh_value

        # 如果没有找到翻译，返回原文加标记
        return f"{term} (未翻译)"

    return term

def analyze_text_with_gemini(text_to_analyze: str, prompt_type: str = "summarize") -> str:
    # Placeholder for Gemini API analysis
    # In a real implementation, this would involve:
    # 1. Importing the 'google.generativeai' library.
    # 2. Configuring the API key.
    # 3. Selecting a model.
    # 4. Crafting a specific prompt based on 'prompt_type'.
    # 5. Calling model.generate_content(prompt).
    # 6. Parsing the response.
    # 7. Handling potential errors or empty responses.

    print(f"Stub: Analyzing text with Gemini (prompt type: {prompt_type}): '{text_to_analyze[:50]}...'")

    if not text_to_analyze:
        return "Error: No text provided for analysis."

    if prompt_type == "summarize":
        return f"Gemini Summary Stub: The provided text appears to describe findings related to '{text_to_analyze.split()[0] if text_to_analyze else 'N/A'}' and suggests further investigation."
    elif prompt_type == "extract_key_findings":
        # This could eventually return a JSON string or a list of strings
        return f"Gemini Key Findings Stub: 1. Potential '{text_to_analyze.split()[0] if text_to_analyze else 'N/A'}'. 2. Associated inflammation noted. 3. Follow-up recommended."
    elif prompt_type == "structured_output_example":
        # Simulate returning a JSON string for structured data
        return '''
        {
            "findings": [
                {"description": "Opacity in the left lower lobe", "location": "Left lower lobe", "type": "Opacity"},
                {"description": "Pleural effusion", "location": "Pleural space", "type": "Effusion"}
            ],
            "impression": "Findings consistent with pneumonia."
        }
        '''
    else:
        return "Error: Unknown prompt type for Gemini analysis."
