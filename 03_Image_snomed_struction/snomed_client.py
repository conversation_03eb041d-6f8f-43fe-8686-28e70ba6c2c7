"""
真实的 SNOMED CT API 客户端
使用 Snowstorm SNOMED CT 服务器进行术语查询和分析
"""

import asyncio
import logging
import json
from typing import List, Dict, Optional, Any, Tuple
from models import SnomedEntity
from config import settings

# 尝试导入httpx，如果不可用则使用替代方案
try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    import urllib.request
    import urllib.parse

# 设置日志
logger = logging.getLogger(__name__)


class SnomedCTClient:
    """SNOMED CT API 客户端 - 增强版"""

    def __init__(self):
        self.base_url = settings.snomed_api_base_url
        self.timeout = settings.snomed_api_timeout
        self.branch = settings.snomed_branch
        self.edition = settings.snomed_edition
        # 添加缓存机制
        self._concept_cache = {}
        self._relationship_cache = {}
        # 添加验证状态
        self._api_validated = False
        # 添加重试机制
        self.max_retries = 3
        self.retry_delay = 1.0

    async def validate_api_connection(self) -> bool:
        """
        验证SNOMED CT API连接和权限

        Returns:
            bool: API是否可用
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # 测试基础连接
                url = f"{self.base_url}/browser/{self.branch}/concepts"
                params = {
                    "term": "heart",
                    "activeFilter": True,
                    "limit": 1,
                    "searchMode": "STANDARD",
                    "lang": "en"
                }

                response = await client.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                if data.get("items"):
                    self._api_validated = True
                    logger.info("SNOMED CT API连接验证成功")
                    return True
                else:
                    logger.warning("SNOMED CT API连接成功但无返回数据")
                    return False

        except Exception as e:
            logger.error(f"SNOMED CT API验证失败: {e}")
            self._api_validated = False
            return False

    async def search_concepts(self, term: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        搜索 SNOMED CT 概念（带缓存和重试机制）

        Args:
            term: 搜索词
            limit: 返回结果数量限制

        Returns:
            概念列表
        """
        # 检查缓存
        cache_key = f"search_{term}_{limit}"
        if cache_key in self._concept_cache:
            logger.debug(f"从缓存获取搜索结果: {term}")
            return self._concept_cache[cache_key]

        # 重试机制
        for attempt in range(self.max_retries):
            try:
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    url = f"{self.base_url}/browser/{self.branch}/concepts"
                    params = {
                        "term": term,
                        "activeFilter": True,
                        "limit": limit,
                        "searchMode": "STANDARD",
                        "lang": "en"
                    }

                    response = await client.get(url, params=params)
                    response.raise_for_status()

                    data = response.json()
                    results = data.get("items", [])

                    # 缓存结果
                    self._concept_cache[cache_key] = results
                    return results

            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"搜索 SNOMED CT 概念失败 (尝试 {attempt + 1}/{self.max_retries}): {term}, 错误: {e}")
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error(f"搜索 SNOMED CT 概念最终失败: {term}, 错误: {e}")
                    return []

    async def get_concept_details(self, concept_id: str) -> Optional[Dict[str, Any]]:
        """
        获取概念详细信息

        Args:
            concept_id: SNOMED CT 概念ID

        Returns:
            概念详细信息
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/browser/{self.branch}/concepts/{concept_id}"

                response = await client.get(url)
                response.raise_for_status()

                return response.json()

        except Exception as e:
            logger.error(f"获取概念详情失败: {concept_id}, 错误: {e}")
            return None

    async def get_concept_parents(self, concept_id: str) -> List[Dict[str, Any]]:
        """
        获取概念的父概念

        Args:
            concept_id: SNOMED CT 概念ID

        Returns:
            父概念列表
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/browser/{self.branch}/concepts/{concept_id}/parents"
                params = {"form": "inferred"}

                response = await client.get(url, params=params)
                response.raise_for_status()

                return response.json()

        except Exception as e:
            logger.error(f"获取父概念失败: {concept_id}, 错误: {e}")
            return []

    async def get_concept_children(self, concept_id: str) -> List[Dict[str, Any]]:
        """
        获取概念的子概念

        Args:
            concept_id: SNOMED CT 概念ID

        Returns:
            子概念列表
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/browser/{self.branch}/concepts/{concept_id}/children"
                params = {"form": "inferred"}

                response = await client.get(url, params=params)
                response.raise_for_status()

                return response.json()

        except Exception as e:
            logger.error(f"获取子概念失败: {concept_id}, 错误: {e}")
            return []

    async def get_concept_ancestors(self, concept_id: str) -> List[Dict[str, Any]]:
        """
        获取概念的所有祖先概念

        Args:
            concept_id: SNOMED CT 概念ID

        Returns:
            祖先概念列表
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/browser/{self.branch}/concepts/{concept_id}/ancestors"
                params = {"form": "inferred"}

                response = await client.get(url, params=params)
                response.raise_for_status()

                return response.json()

        except Exception as e:
            logger.error(f"获取祖先概念失败: {concept_id}, 错误: {e}")
            return []

    async def find_best_match(self, term: str, semantic_tags: List[str] = None) -> Optional[SnomedEntity]:
        """
        为给定术语找到最佳匹配的 SNOMED CT 概念

        Args:
            term: 医学术语
            semantic_tags: 语义标签过滤器

        Returns:
            最佳匹配的 SnomedEntity
        """
        concepts = await self.search_concepts(term, limit=10)

        if not concepts:
            return None

        # 如果指定了语义标签，进行过滤
        if semantic_tags:
            filtered_concepts = []
            for concept in concepts:
                fsn = concept.get("fsn", {}).get("term", "")
                for tag in semantic_tags:
                    if tag.lower() in fsn.lower():
                        filtered_concepts.append(concept)
                        break
            concepts = filtered_concepts if filtered_concepts else concepts

        # 选择最佳匹配（通常是第一个结果）
        best_match = concepts[0]

        # 获取详细信息
        concept_details = await self.get_concept_details(best_match["conceptId"])

        if not concept_details:
            return None

        # 获取父概念信息用于构建关系
        parents = await self.get_concept_parents(best_match["conceptId"])
        parent_relationships = {}
        if parents:
            parent_relationships["parent_codes"] = [p["conceptId"] for p in parents]

        return SnomedEntity(
            term=best_match.get("pt", {}).get("term", ""),
            code=best_match["conceptId"],
            relationships=parent_relationships
        )

    async def build_concept_hierarchy(self, concept_ids: List[str]) -> List[SnomedEntity]:
        """
        为给定的概念ID列表构建层次结构

        Args:
            concept_ids: SNOMED CT 概念ID列表

        Returns:
            构建好层次关系的 SnomedEntity 列表
        """
        entities = []
        concept_map = {}

        # 获取所有概念的详细信息
        for concept_id in concept_ids:
            concept_details = await self.get_concept_details(concept_id)
            if concept_details:
                parents = await self.get_concept_parents(concept_id)

                parent_relationships = {}
                if parents:
                    # 只取直接父概念
                    direct_parents = [p["conceptId"] for p in parents if p["conceptId"] in concept_ids]
                    if direct_parents:
                        parent_relationships["parent_code"] = direct_parents[0]  # 取第一个父概念

                entity = SnomedEntity(
                    term=concept_details.get("pt", {}).get("term", ""),
                    code=concept_id,
                    relationships=parent_relationships
                )

                entities.append(entity)
                concept_map[concept_id] = entity

        return entities


class MedicalNLPProcessor:
    """医学自然语言处理器"""

    def __init__(self):
        self.snomed_client = SnomedCTClient()

    async def extract_medical_terms(self, text: str) -> List[str]:
        """
        从文本中提取医学术语

        Args:
            text: 输入文本

        Returns:
            提取的医学术语列表
        """
        # 简单的关键词提取（实际应用中应使用更高级的NLP模型）
        medical_keywords = [
            # 病理发现
            "pneumonia", "fracture", "tumor", "cancer", "inflammation",
            "infection", "hemorrhage", "bleeding", "edema", "swelling",
            "atrophy", "hypertrophy", "stenosis", "occlusion", "thrombosis",

            # 解剖结构
            "lung", "heart", "liver", "kidney", "brain", "spine", "bone",
            "muscle", "vessel", "artery", "vein", "nerve", "joint",

            # 症状
            "pain", "fever", "cough", "dyspnea", "headache", "nausea",
            "vomiting", "diarrhea", "constipation", "fatigue",

            # 中文医学术语
            "肺炎", "骨折", "肿瘤", "癌症", "炎症", "感染", "出血",
            "水肿", "萎缩", "狭窄", "阻塞", "血栓",
            "肺", "心脏", "肝脏", "肾脏", "大脑", "脊柱", "骨骼",
            "疼痛", "发热", "咳嗽", "呼吸困难", "头痛", "恶心"
        ]

        text_lower = text.lower()
        found_terms = []

        for keyword in medical_keywords:
            if keyword.lower() in text_lower:
                found_terms.append(keyword)

        return list(set(found_terms))  # 去重

    async def analyze_medical_text(self, text: str) -> List[SnomedEntity]:
        """
        分析医学文本并返回 SNOMED CT 实体

        Args:
            text: 医学文本

        Returns:
            SNOMED CT 实体列表
        """
        # 提取医学术语
        terms = await self.extract_medical_terms(text)

        entities = []

        # 为每个术语查找 SNOMED CT 概念
        for term in terms:
            entity = await self.snomed_client.find_best_match(term)
            if entity:
                entities.append(entity)

        return entities


    def clear_cache(self):
        """清空缓存"""
        self._concept_cache.clear()
        self._relationship_cache.clear()
        logger.info("SNOMED CT缓存已清空")

    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计"""
        return {
            "concept_cache_size": len(self._concept_cache),
            "relationship_cache_size": len(self._relationship_cache)
        }

    async def validate_api_connection(self) -> bool:
        """
        验证SNOMED CT API连接和权限

        Returns:
            bool: API是否可用
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # 测试基础连接
                url = f"{self.base_url}/browser/{self.branch}/concepts"
                params = {
                    "term": "heart",
                    "activeFilter": True,
                    "limit": 1,
                    "searchMode": "STANDARD",
                    "lang": "en"
                }

                response = await client.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                if data.get("items"):
                    self._api_validated = True
                    logger.info("SNOMED CT API连接验证成功")
                    return True
                else:
                    logger.warning("SNOMED CT API连接成功但无返回数据")
                    return False

        except Exception as e:
            logger.error(f"SNOMED CT API验证失败: {e}")
            self._api_validated = False
            return False


# 创建全局实例
snomed_client = SnomedCTClient()
nlp_processor = MedicalNLPProcessor()
