# 应用配置
APP_NAME="SNOMED CT Medical Report Analyzer"
APP_VERSION="2.0.0"
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=false

# SNOMED CT API 配置
SNOMED_API_BASE_URL=https://snowstorm.ihtsdotools.org/snowstorm/snomed-ct
SNOMED_API_TIMEOUT=30
SNOMED_BRANCH=MAIN
SNOMED_EDITION=en-edition

# LOINC API 配置
LOINC_API_BASE_URL=https://fhir.loinc.org
LOINC_API_TIMEOUT=30

# Google Gemini API 配置
GEMINI_API_KEY=my-google-api-key
GEMINI_MODEL=gemini-2.5-flash

# OpenAI API 配置 (备用)
OPENAI_API_KEY=my-openai-api-key
OPENAI_MODEL=gpt-4o-mini

# NLP 模型配置
SPACY_MODEL=en_core_sci_sm
BIOBERT_MODEL=dmis-lab/biobert-base-cased-v1.2

# 数据库配置
DATABASE_URL=sqlite:///./snomed_cache.db

# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 文件存储配置
UPLOAD_DIR=uploads
HISTORY_DIR=history_records
MAX_FILE_SIZE=10485760

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS 配置
ALLOWED_ORIGINS=["*"]
ALLOWED_METHODS=["*"]
ALLOWED_HEADERS=["*"]
