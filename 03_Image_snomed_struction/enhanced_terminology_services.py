"""
增强的术语服务模块
整合SNOMED CT、LOINC和本地医学术语库，提供统一的术语映射和验证服务
"""

import asyncio
import json
import logging
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

from models import SnomedEntity, LoincTerm
from snomed_client import snomed_client, nlp_processor
from config import IMAGING_LOINC_MAPPING, MEDICAL_TERMS_TRANSLATION

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TerminologyValidationResult:
    """术语验证结果"""
    is_valid: bool
    confidence: float
    snomed_entity: Optional[SnomedEntity]
    suggestions: List[str]
    validation_source: str
    timestamp: datetime

@dataclass
class MappingResult:
    """术语映射结果"""
    original_term: str
    mapped_entities: List[SnomedEntity]
    loinc_term: Optional[LoincTerm]
    confidence_score: float
    mapping_method: str
    alternatives: List[Dict[str, Any]]

class EnhancedTerminologyService:
    """增强的术语服务"""
    
    def __init__(self):
        self.snomed_client = snomed_client
        self.nlp_processor = nlp_processor
        
        # 本地术语缓存
        self._local_cache = {}
        self._validation_cache = {}
        
        # 术语质量评估
        self._quality_threshold = 0.7
        
        # 初始化状态
        self._initialized = False
        
    async def initialize(self) -> bool:
        """
        初始化术语服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 验证SNOMED CT API连接
            api_valid = await self.snomed_client.validate_api_connection()
            if not api_valid:
                logger.warning("SNOMED CT API连接失败，将使用本地回退模式")
            
            # 加载本地术语库
            await self._load_local_terminology()
            
            self._initialized = True
            logger.info("增强术语服务初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"术语服务初始化失败: {e}")
            return False
    
    async def _load_local_terminology(self):
        """加载本地术语库"""
        try:
            # 这里可以加载本地的医学术语数据库
            # 例如从文件、数据库或其他来源加载
            logger.info("本地术语库加载完成")
        except Exception as e:
            logger.error(f"加载本地术语库失败: {e}")
    
    async def validate_medical_term(self, term: str, context: str = "") -> TerminologyValidationResult:
        """
        验证医学术语的有效性
        
        Args:
            term: 医学术语
            context: 上下文信息
            
        Returns:
            TerminologyValidationResult: 验证结果
        """
        # 检查缓存
        cache_key = f"validate_{term}_{context}"
        if cache_key in self._validation_cache:
            return self._validation_cache[cache_key]
        
        try:
            # 使用SNOMED CT验证
            snomed_entity = await self.snomed_client.find_best_match(term)
            
            if snomed_entity:
                confidence = 0.9  # 找到精确匹配
                suggestions = []
                validation_source = "SNOMED_CT"
                is_valid = True
            else:
                # 尝试模糊匹配
                concepts = await self.snomed_client.search_concepts(term, limit=5)
                if concepts:
                    confidence = 0.6  # 找到相似匹配
                    suggestions = [c.get("pt", {}).get("term", "") for c in concepts[:3]]
                    validation_source = "SNOMED_CT_FUZZY"
                    is_valid = False
                else:
                    confidence = 0.1  # 未找到匹配
                    suggestions = await self._get_local_suggestions(term)
                    validation_source = "LOCAL"
                    is_valid = False
            
            result = TerminologyValidationResult(
                is_valid=is_valid,
                confidence=confidence,
                snomed_entity=snomed_entity,
                suggestions=suggestions,
                validation_source=validation_source,
                timestamp=datetime.now()
            )
            
            # 缓存结果
            self._validation_cache[cache_key] = result
            return result
            
        except Exception as e:
            logger.error(f"术语验证失败: {term}, 错误: {e}")
            return TerminologyValidationResult(
                is_valid=False,
                confidence=0.0,
                snomed_entity=None,
                suggestions=[],
                validation_source="ERROR",
                timestamp=datetime.now()
            )
    
    async def _get_local_suggestions(self, term: str) -> List[str]:
        """获取本地术语建议"""
        suggestions = []
        
        # 基于编辑距离的模糊匹配
        for local_term in MEDICAL_TERMS_TRANSLATION.keys():
            if self._calculate_similarity(term.lower(), local_term.lower()) > 0.6:
                suggestions.append(local_term)
        
        return suggestions[:3]
    
    def _calculate_similarity(self, term1: str, term2: str) -> float:
        """计算术语相似度（简单的编辑距离实现）"""
        if not term1 or not term2:
            return 0.0
        
        # 简单的相似度计算
        common_chars = set(term1) & set(term2)
        total_chars = set(term1) | set(term2)
        
        if not total_chars:
            return 0.0
        
        return len(common_chars) / len(total_chars)
    
    async def map_text_to_terminology(self, text: str, include_loinc: bool = True) -> MappingResult:
        """
        将文本映射到标准术语
        
        Args:
            text: 医学文本
            include_loinc: 是否包含LOINC映射
            
        Returns:
            MappingResult: 映射结果
        """
        try:
            # 提取SNOMED CT实体
            snomed_entities = await self.nlp_processor.analyze_medical_text(text)
            
            # 获取LOINC术语（如果需要）
            loinc_term = None
            if include_loinc:
                loinc_term = self._get_loinc_for_text(text)
            
            # 计算置信度
            confidence_score = self._calculate_mapping_confidence(snomed_entities, text)
            
            # 生成替代方案
            alternatives = await self._generate_alternatives(text, snomed_entities)
            
            return MappingResult(
                original_term=text,
                mapped_entities=snomed_entities,
                loinc_term=loinc_term,
                confidence_score=confidence_score,
                mapping_method="NLP_ENHANCED",
                alternatives=alternatives
            )
            
        except Exception as e:
            logger.error(f"术语映射失败: {text}, 错误: {e}")
            return MappingResult(
                original_term=text,
                mapped_entities=[],
                loinc_term=None,
                confidence_score=0.0,
                mapping_method="ERROR",
                alternatives=[]
            )
    
    def _get_loinc_for_text(self, text: str) -> Optional[LoincTerm]:
        """为文本获取LOINC术语"""
        text_lower = text.lower()
        
        for key, loinc_code in IMAGING_LOINC_MAPPING.items():
            if key in text_lower:
                term_name = self._get_loinc_term_name(key)
                return LoincTerm(term=term_name, code=loinc_code)
        
        return None
    
    def _get_loinc_term_name(self, key: str) -> str:
        """获取LOINC术语名称"""
        name_mapping = {
            "chest x-ray": "Chest X-ray",
            "chest xray": "Chest X-ray", 
            "chest radiograph": "Chest radiograph",
            "胸部x光": "胸部X光",
            "胸片": "胸部X光片",
            "ct chest": "CT chest",
            "chest ct": "CT chest",
            "胸部ct": "胸部CT",
            "mri brain": "MRI brain",
            "brain mri": "MRI brain",
            "头部mri": "头部MRI",
            "脑部mri": "脑部MRI"
        }
        return name_mapping.get(key, key.title())
    
    def _calculate_mapping_confidence(self, entities: List[SnomedEntity], original_text: str) -> float:
        """计算映射置信度"""
        if not entities:
            return 0.0
        
        # 基于实体数量和文本长度的简单置信度计算
        entity_coverage = len(entities) / max(len(original_text.split()), 1)
        base_confidence = min(entity_coverage * 0.8, 0.9)
        
        return base_confidence
    
    async def _generate_alternatives(self, text: str, entities: List[SnomedEntity]) -> List[Dict[str, Any]]:
        """生成替代映射方案"""
        alternatives = []
        
        # 如果当前映射置信度较低，尝试生成替代方案
        if len(entities) < 2:
            # 尝试不同的搜索策略
            words = text.split()
            for word in words:
                if len(word) > 3:  # 忽略短词
                    alt_entities = await self.snomed_client.search_concepts(word, limit=3)
                    if alt_entities:
                        alternatives.append({
                            "method": "word_based",
                            "term": word,
                            "concepts": alt_entities[:2]
                        })
        
        return alternatives

# 创建全局实例
enhanced_terminology_service = EnhancedTerminologyService()
