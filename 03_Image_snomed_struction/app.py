"""
SNOMED CT 医学影像报告结构化分析系统 - FastAPI 服务
"""

import time
import uuid
from datetime import datetime
from typing import List, Dict, Any

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger
import uvicorn

from config import settings
from api_models import *
from models import ImagingReport, PatientInfo, ReportAnalysis
from terminology_services import (
    extract_snomed_entities_async,
    get_loinc_code_for_examination,
    translate_term,
    analyze_text_with_gemini
)
from utils import build_snomed_tree
from history_manager import save_analysis, load_all_analyses
from snomed_client import snomed_client, nlp_processor
import io

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于 SNOMED CT 的医学影像报告结构化分析系统",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

# 配置日志
logger.add(
    settings.log_file,
    rotation="1 day",
    retention="30 days",
    level=settings.log_level
)


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info(f"启动 {settings.app_name} v{settings.app_version}")
    logger.info(f"SNOMED CT API: {settings.snomed_api_base_url}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("关闭应用")


@app.get("/", response_model=HealthCheckResponse)
async def root():
    """根路径 - 健康检查"""
    return HealthCheckResponse(
        status="healthy",
        version=settings.app_version,
        services={
            "snomed_api": "available",
            "nlp_processor": "available",
            "database": "available"
        }
    )


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查端点"""
    try:
        # 测试 SNOMED CT API 连接
        test_concepts = await snomed_client.search_concepts("pneumonia", limit=1)
        snomed_status = "available" if test_concepts else "unavailable"

        return HealthCheckResponse(
            status="healthy",
            version=settings.app_version,
            services={
                "snomed_api": snomed_status,
                "nlp_processor": "available",
                "database": "available"
            }
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return HealthCheckResponse(
            status="degraded",
            version=settings.app_version,
            services={
                "snomed_api": "unavailable",
                "nlp_processor": "available",
                "database": "available"
            }
        )


@app.post("/analyze/report", response_model=AnalysisResponse)
async def analyze_imaging_report(request: ImagingReportRequest):
    """分析完整的影像报告"""
    start_time = time.time()
    report_id = str(uuid.uuid4())

    try:
        logger.info(f"开始分析报告 {report_id}")

        # 创建报告对象
        patient_info = PatientInfo(
            age=request.patient_info.age,
            sex=request.patient_info.sex
        )

        report = ImagingReport(
            patient_info=patient_info,
            chief_complaint=request.chief_complaint,
            examination_requested=request.examination_requested,
            imaging_findings=request.imaging_findings,
            diagnostic_impression=request.diagnostic_impression
        )

        # 提取 SNOMED CT 实体
        findings_entities = await extract_snomed_entities_async(report.imaging_findings)
        impression_entities = await extract_snomed_entities_async(report.diagnostic_impression)
        all_entities = findings_entities + impression_entities

        # 构建层次结构
        tree_entities = build_snomed_tree(all_entities)
        tree_output = ""
        if tree_entities:
            tree_string_io = io.StringIO()
            for root in tree_entities:
                tree_string_io.write(str(root))
            tree_output = tree_string_io.getvalue()
            tree_string_io.close()

        # 获取 LOINC 代码
        loinc_code = get_loinc_code_for_examination(report.examination_requested)

        # Gemini 分析
        gemini_summary = analyze_text_with_gemini(
            f"{report.imaging_findings}\n{report.diagnostic_impression}",
            "summarize"
        )

        # 提取关键发现
        key_findings_text = analyze_text_with_gemini(
            f"{report.imaging_findings}\n{report.diagnostic_impression}",
            "extract_key_findings"
        )
        key_findings = [finding.strip() for finding in key_findings_text.split('\n') if finding.strip()]

        # 转换实体为响应格式
        snomed_responses = []
        for entity in all_entities:
            translation = translate_term(entity.term, "zh")
            snomed_responses.append(SnomedEntityResponse(
                term=entity.term,
                code=entity.code,
                relationships=entity.relationships,
                translation=translation
            ))

        # 创建分析结果
        analysis_duration = time.time() - start_time

        response = AnalysisResponse(
            report_id=report_id,
            timestamp=datetime.now(),
            patient_info=request.patient_info,
            examination_requested=request.examination_requested,
            loinc_code=LoincTermResponse(
                term=loinc_code.term,
                code=loinc_code.code
            ) if loinc_code else None,
            chief_complaint=request.chief_complaint,
            imaging_findings=request.imaging_findings,
            diagnostic_impression=request.diagnostic_impression,
            snomed_entities=snomed_responses,
            structured_tree_output=tree_output,
            gemini_analysis_summary=gemini_summary,
            key_findings=key_findings,
            entity_count=len(all_entities),
            analysis_duration=analysis_duration
        )

        # 保存分析结果（后台任务）
        report.snomed_entities = all_entities
        if loinc_code:
            report.loinc_code = loinc_code

        analysis = ReportAnalysis(
            report=report,
            gemini_analysis_summary=gemini_summary,
            structured_tree_output=tree_output
        )

        # 异步保存
        save_analysis(analysis)

        logger.info(f"报告分析完成 {report_id}, 耗时: {analysis_duration:.2f}秒")
        return response

    except Exception as e:
        logger.error(f"分析报告失败 {report_id}: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@app.post("/analyze/text", response_model=TextAnalysisResponse)
async def analyze_text(request: TextAnalysisRequest):
    """分析单段文本"""
    start_time = time.time()

    try:
        logger.info(f"开始分析文本: {request.text[:50]}...")

        # 提取 SNOMED CT 实体
        entities = await extract_snomed_entities_async(request.text, request.language)

        # 构建层次结构
        tree_entities = build_snomed_tree(entities)
        tree_output = ""
        if tree_entities:
            tree_string_io = io.StringIO()
            for root in tree_entities:
                tree_string_io.write(str(root))
            tree_output = tree_string_io.getvalue()
            tree_string_io.close()

        # 转换实体为响应格式
        snomed_responses = []
        for entity in entities:
            translation = translate_term(entity.term, "zh")
            snomed_responses.append(SnomedEntityResponse(
                term=entity.term,
                code=entity.code,
                relationships=entity.relationships,
                translation=translation
            ))

        # AI 分析（如果需要）
        summary = ""
        key_findings = []
        if request.analysis_type in ["full", "summary_only"]:
            summary = analyze_text_with_gemini(request.text, "summarize")
            key_findings_text = analyze_text_with_gemini(request.text, "extract_key_findings")
            key_findings = [finding.strip() for finding in key_findings_text.split('\n') if finding.strip()]

        processing_time = time.time() - start_time

        response = TextAnalysisResponse(
            text=request.text,
            language=request.language,
            snomed_entities=snomed_responses,
            hierarchy_tree=tree_output,
            summary=summary,
            key_findings=key_findings,
            processing_time=processing_time
        )

        logger.info(f"文本分析完成, 耗时: {processing_time:.2f}秒")
        return response

    except Exception as e:
        logger.error(f"文本分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@app.get("/history", response_model=HistoryResponse)
async def get_analysis_history():
    """获取分析历史记录"""
    try:
        analyses = load_all_analyses()

        # 转换为响应格式
        response_analyses = []
        for analysis in analyses:
            if analysis.report and analysis.report.patient_info:
                patient_info = PatientInfoRequest(
                    age=analysis.report.patient_info.age,
                    sex=analysis.report.patient_info.sex
                )

                # 转换 SNOMED 实体
                snomed_responses = []
                for entity in analysis.report.snomed_entities:
                    translation = translate_term(entity.term, "zh")
                    snomed_responses.append(SnomedEntityResponse(
                        term=entity.term,
                        code=entity.code,
                        relationships=entity.relationships,
                        translation=translation
                    ))

                response_analysis = AnalysisResponse(
                    report_id=str(uuid.uuid4()),  # 生成临时ID
                    timestamp=datetime.fromisoformat(analysis.report.timestamp),
                    patient_info=patient_info,
                    examination_requested=analysis.report.examination_requested,
                    loinc_code=LoincTermResponse(
                        term=analysis.report.loinc_code.term,
                        code=analysis.report.loinc_code.code
                    ) if analysis.report.loinc_code else None,
                    chief_complaint=analysis.report.chief_complaint,
                    imaging_findings=analysis.report.imaging_findings,
                    diagnostic_impression=analysis.report.diagnostic_impression,
                    snomed_entities=snomed_responses,
                    structured_tree_output=analysis.structured_tree_output,
                    gemini_analysis_summary=analysis.gemini_analysis_summary,
                    entity_count=len(analysis.report.snomed_entities)
                )

                response_analyses.append(response_analysis)

        return HistoryResponse(
            total_count=len(response_analyses),
            analyses=response_analyses
        )

    except Exception as e:
        logger.error(f"获取历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")


@app.post("/snomed/search", response_model=SnomedSearchResponse)
async def search_snomed_concepts(request: SnomedSearchRequest):
    """搜索 SNOMED CT 概念"""
    try:
        logger.info(f"搜索 SNOMED CT 概念: {request.term}")

        concepts = await snomed_client.search_concepts(request.term, request.limit)

        results = []
        for concept in concepts:
            # 获取概念详情
            details = await snomed_client.get_concept_details(concept["conceptId"])
            if details:
                translation = translate_term(concept.get("pt", {}).get("term", ""), "zh")
                results.append(SnomedEntityResponse(
                    term=concept.get("pt", {}).get("term", ""),
                    code=concept["conceptId"],
                    relationships={},
                    translation=translation
                ))

        return SnomedSearchResponse(
            query=request.term,
            total_found=len(results),
            results=results
        )

    except Exception as e:
        logger.error(f"搜索 SNOMED CT 概念失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@app.get("/snomed/concept/{concept_id}/hierarchy", response_model=ConceptHierarchyResponse)
async def get_concept_hierarchy(concept_id: str):
    """获取概念的层次结构信息"""
    try:
        logger.info(f"获取概念层次结构: {concept_id}")

        # 获取概念详情
        concept_details = await snomed_client.get_concept_details(concept_id)
        if not concept_details:
            raise HTTPException(status_code=404, detail="概念未找到")

        # 获取父概念
        parents_data = await snomed_client.get_concept_parents(concept_id)
        parents = []
        for parent in parents_data:
            translation = translate_term(parent.get("pt", {}).get("term", ""), "zh")
            parents.append(SnomedEntityResponse(
                term=parent.get("pt", {}).get("term", ""),
                code=parent["conceptId"],
                relationships={},
                translation=translation
            ))

        # 获取子概念
        children_data = await snomed_client.get_concept_children(concept_id)
        children = []
        for child in children_data:
            translation = translate_term(child.get("pt", {}).get("term", ""), "zh")
            children.append(SnomedEntityResponse(
                term=child.get("pt", {}).get("term", ""),
                code=child["conceptId"],
                relationships={},
                translation=translation
            ))

        # 获取祖先概念
        ancestors_data = await snomed_client.get_concept_ancestors(concept_id)
        ancestors = []
        for ancestor in ancestors_data:
            translation = translate_term(ancestor.get("pt", {}).get("term", ""), "zh")
            ancestors.append(SnomedEntityResponse(
                term=ancestor.get("pt", {}).get("term", ""),
                code=ancestor["conceptId"],
                relationships={},
                translation=translation
            ))

        return ConceptHierarchyResponse(
            concept_id=concept_id,
            concept_term=concept_details.get("pt", {}).get("term", ""),
            parents=parents,
            children=children,
            ancestors=ancestors
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取概念层次结构失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取层次结构失败: {str(e)}")


@app.post("/analyze/batch", response_model=BatchAnalysisResponse)
async def batch_analyze_reports(request: BatchAnalysisRequest):
    """批量分析影像报告"""
    start_time = time.time()

    try:
        logger.info(f"开始批量分析 {len(request.reports)} 个报告")

        results = []
        errors = []
        successful_count = 0
        failed_count = 0

        for i, report_request in enumerate(request.reports):
            try:
                # 分析单个报告
                analysis_result = await analyze_imaging_report(report_request)
                results.append(analysis_result)
                successful_count += 1

            except Exception as e:
                logger.error(f"批量分析第 {i+1} 个报告失败: {e}")
                errors.append(ErrorResponse(
                    error="analysis_failed",
                    message=f"第 {i+1} 个报告分析失败",
                    details={"index": i, "error": str(e)}
                ))
                failed_count += 1

        total_processing_time = time.time() - start_time

        return BatchAnalysisResponse(
            total_reports=len(request.reports),
            successful_analyses=successful_count,
            failed_analyses=failed_count,
            results=results,
            errors=errors,
            total_processing_time=total_processing_time
        )

    except Exception as e:
        logger.error(f"批量分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量分析失败: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
