#!/usr/bin/env python3
"""
SNOMED CT 医学影像报告结构化分析系统启动脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import httpx
        import loguru
        print("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def setup_directories():
    """创建必要的目录"""
    directories = ["logs", "uploads", "history_records"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_name}")

def check_env_file():
    """检查环境配置文件"""
    if not Path(".env").exists():
        if Path(".env.example").exists():
            print("⚠️  未找到 .env 文件，请复制 .env.example 并配置")
            print("cp .env.example .env")
        else:
            print("⚠️  未找到环境配置文件")
        return False
    print("✅ 环境配置文件检查通过")
    return True

def install_spacy_models():
    """安装 spaCy 医学模型"""
    try:
        import spacy
        # 检查是否已安装医学模型
        try:
            nlp = spacy.load("en_core_sci_sm")
            print("✅ spaCy 医学模型已安装")
            return True
        except OSError:
            print("📦 正在安装 spaCy 医学模型...")
            # 安装 scispacy 模型
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.3/en_core_sci_sm-0.5.3.tar.gz"
            ], check=True)
            print("✅ spaCy 医学模型安装完成")
            return True
    except Exception as e:
        print(f"⚠️  spaCy 模型安装失败: {e}")
        print("可以手动安装: pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.3/en_core_sci_sm-0.5.3.tar.gz")
        return False

def test_snomed_api():
    """测试 SNOMED CT API 连接"""
    try:
        import httpx
        import asyncio
        
        async def test_connection():
            try:
                async with httpx.AsyncClient(timeout=10) as client:
                    response = await client.get(
                        "https://snowstorm.ihtsdotools.org/snowstorm/snomed-ct/browser/MAIN/concepts",
                        params={"term": "pneumonia", "limit": 1}
                    )
                    if response.status_code == 200:
                        print("✅ SNOMED CT API 连接正常")
                        return True
                    else:
                        print(f"⚠️  SNOMED CT API 响应异常: {response.status_code}")
                        return False
            except Exception as e:
                print(f"⚠️  SNOMED CT API 连接失败: {e}")
                print("将使用回退模式运行")
                return False
        
        return asyncio.run(test_connection())
    except Exception as e:
        print(f"⚠️  无法测试 SNOMED CT API: {e}")
        return False

def start_server(host="0.0.0.0", port=8000, reload=False, workers=1):
    """启动服务器"""
    print(f"🚀 启动服务器: http://{host}:{port}")
    print(f"📚 API 文档: http://{host}:{port}/docs")
    print(f"📖 ReDoc 文档: http://{host}:{port}/redoc")
    
    cmd = [
        "uvicorn", "app:app",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    
    if workers > 1:
        cmd.extend(["--workers", str(workers)])
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def main():
    parser = argparse.ArgumentParser(description="SNOMED CT 医学影像报告分析系统")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开启自动重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    parser.add_argument("--skip-checks", action="store_true", help="跳过环境检查")
    parser.add_argument("--install-models", action="store_true", help="安装 NLP 模型")
    
    args = parser.parse_args()
    
    print("🏥 SNOMED CT 医学影像报告结构化分析系统")
    print("=" * 50)
    
    if not args.skip_checks:
        print("🔍 正在进行环境检查...")
        
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        # 创建目录
        setup_directories()
        
        # 检查环境文件
        check_env_file()
        
        # 安装模型
        if args.install_models:
            install_spacy_models()
        
        # 测试 API 连接
        test_snomed_api()
        
        print("✅ 环境检查完成")
        print("-" * 50)
    
    # 启动服务器
    start_server(
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers
    )

if __name__ == "__main__":
    main()
