"""
双向驱动的报告模版引擎
支持影像→诊断和诊断→描述两种工作流
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import os

from .llm_client import LLMClient

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TemplateField:
    """模版字段"""
    name: str
    field_type: str  # 'text', 'select', 'multiselect', 'number'
    required: bool
    options: List[str] = None
    default_value: str = ""
    description: str = ""
    snomed_mapping: str = ""

@dataclass
class ReportTemplate:
    """报告模版"""
    template_id: str
    name: str
    category: str  # 'chest_ct', 'brain_mri', etc.
    version: str
    fields: List[TemplateField]
    generation_rules: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

@dataclass
class DiagnosisTemplate:
    """诊断模版"""
    diagnosis_name: str
    snomed_code: str
    description_templates: List[str]
    required_findings: List[str]
    optional_findings: List[str]
    differential_diagnoses: List[str]

class BidirectionalTemplateEngine:
    """双向驱动的报告模版引擎"""

    def __init__(self, llm_client: LLMClient, template_dir: str = None):
        self.llm_client = llm_client

        # 设置模版存储目录
        if template_dir is None:
            base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
            self.template_dir = os.path.join(base_dir, "templates")
        else:
            self.template_dir = template_dir

        os.makedirs(self.template_dir, exist_ok=True)

        # 加载模版
        self.report_templates = {}
        self.diagnosis_templates = {}
        self._load_templates()

        logger.info(f"双向模版引擎初始化完成，加载了 {len(self.report_templates)} 个报告模版和 {len(self.diagnosis_templates)} 个诊断模版")

    def _load_templates(self):
        """加载模版"""
        try:
            # 加载报告模版
            report_template_file = os.path.join(self.template_dir, "report_templates.json")
            if os.path.exists(report_template_file):
                with open(report_template_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for template_data in data.get("templates", []):
                        template = self._dict_to_report_template(template_data)
                        self.report_templates[template.template_id] = template

            # 加载诊断模版
            diagnosis_template_file = os.path.join(self.template_dir, "diagnosis_templates.json")
            if os.path.exists(diagnosis_template_file):
                with open(diagnosis_template_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for diag_data in data.get("diagnoses", []):
                        diagnosis = DiagnosisTemplate(**diag_data)
                        self.diagnosis_templates[diagnosis.diagnosis_name] = diagnosis

            # 如果没有模版，创建默认模版
            if not self.report_templates:
                self._create_default_templates()

        except Exception as e:
            logger.error(f"加载模版失败: {e}")
            self._create_default_templates()

    def _dict_to_report_template(self, data: Dict[str, Any]) -> ReportTemplate:
        """将字典转换为报告模版对象"""
        fields = []
        for field_data in data.get("fields", []):
            field = TemplateField(**field_data)
            fields.append(field)

        return ReportTemplate(
            template_id=data["template_id"],
            name=data["name"],
            category=data["category"],
            version=data["version"],
            fields=fields,
            generation_rules=data.get("generation_rules", {}),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )

    def _create_default_templates(self):
        """创建默认模版"""
        # 创建胸部CT模版
        chest_ct_fields = [
            TemplateField("解剖位置", "select", True,
                         ["右上肺叶", "右中肺叶", "右下肺叶", "左上肺叶", "左下肺叶", "双肺", "纵隔", "胸膜"],
                         "", "病变所在的解剖位置"),
            TemplateField("病变大小", "text", False, [], "", "病变的尺寸描述"),
            TemplateField("病变形态", "select", False,
                         ["结节状", "片状", "条索状", "网格状", "蜂窝状"],
                         "", "病变的形态特征"),
            TemplateField("密度特征", "select", False,
                         ["实性", "半实性", "磨玻璃样", "钙化", "脂肪密度"],
                         "", "病变的密度特征"),
            TemplateField("边界特征", "select", False,
                         ["清晰", "模糊", "毛刺状", "分叶状"],
                         "", "病变边界的特征"),
            TemplateField("增强方式", "select", False,
                         ["明显强化", "轻度强化", "无强化", "环形强化"],
                         "", "增强扫描的强化方式")
        ]

        chest_ct_template = ReportTemplate(
            template_id="chest_ct_standard",
            name="胸部CT标准模版",
            category="chest_ct",
            version="1.0",
            fields=chest_ct_fields,
            generation_rules={
                "影像_to_诊断": {
                    "rules": [
                        {"condition": "结节状 + 毛刺状边界", "diagnosis": "肺癌可能", "confidence": 0.8},
                        {"condition": "磨玻璃样 + 小结节", "diagnosis": "早期肺腺癌可能", "confidence": 0.7},
                        {"condition": "钙化 + 结节", "diagnosis": "良性病变可能", "confidence": 0.9}
                    ]
                },
                "诊断_to_描述": {
                    "templates": {
                        "肺癌": "{解剖位置}可见约{病变大小}大小的{病变形态}{密度特征}影，边缘{边界特征}，增强扫描后呈{增强方式}。",
                        "肺炎": "{解剖位置}可见{病变形态}影，密度{密度特征}，边界{边界特征}。"
                    }
                }
            },
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        self.report_templates["chest_ct_standard"] = chest_ct_template

        # 创建默认诊断模版
        default_diagnoses = [
            DiagnosisTemplate(
                diagnosis_name="肺癌",
                snomed_code="363358000",
                description_templates=[
                    "{解剖位置}可见约{大小}大小的{形态}软组织密度影，边缘{边界}",
                    "增强扫描后呈{增强方式}",
                    "周围可见{其他特征}"
                ],
                required_findings=["结节", "肿块"],
                optional_findings=["毛刺征", "胸膜牵拉", "淋巴结肿大"],
                differential_diagnoses=["肺炎", "结核", "转移瘤"]
            ),
            DiagnosisTemplate(
                diagnosis_name="肺炎",
                snomed_code="233604007",
                description_templates=[
                    "{解剖位置}可见{形态}影",
                    "密度{密度特征}，边界{边界特征}",
                    "可伴有{其他特征}"
                ],
                required_findings=["炎性浸润"],
                optional_findings=["胸腔积液", "淋巴结肿大"],
                differential_diagnoses=["肺癌", "结核", "肺栓塞"]
            )
        ]

        for diagnosis in default_diagnoses:
            self.diagnosis_templates[diagnosis.diagnosis_name] = diagnosis

        # 保存默认模版
        self._save_templates()

    def _save_templates(self):
        """保存模版到文件"""
        try:
            # 保存报告模版
            report_data = {
                "templates": [self._report_template_to_dict(template) for template in self.report_templates.values()]
            }
            report_file = os.path.join(self.template_dir, "report_templates.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

            # 保存诊断模版
            diagnosis_data = {
                "diagnoses": [asdict(diagnosis) for diagnosis in self.diagnosis_templates.values()]
            }
            diagnosis_file = os.path.join(self.template_dir, "diagnosis_templates.json")
            with open(diagnosis_file, 'w', encoding='utf-8') as f:
                json.dump(diagnosis_data, f, ensure_ascii=False, indent=2)

            logger.info("模版保存成功")

        except Exception as e:
            logger.error(f"保存模版失败: {e}")

    def _report_template_to_dict(self, template: ReportTemplate) -> Dict[str, Any]:
        """将报告模版转换为字典"""
        return {
            "template_id": template.template_id,
            "name": template.name,
            "category": template.category,
            "version": template.version,
            "fields": [asdict(field) for field in template.fields],
            "generation_rules": template.generation_rules,
            "created_at": template.created_at.isoformat(),
            "updated_at": template.updated_at.isoformat()
        }

    def get_template_by_category(self, category: str) -> Optional[ReportTemplate]:
        """根据类别获取模版"""
        for template in self.report_templates.values():
            if template.category == category:
                return template
        return None

    def get_diagnosis_template(self, diagnosis_name: str) -> Optional[DiagnosisTemplate]:
        """获取诊断模版"""
        return self.diagnosis_templates.get(diagnosis_name)

    async def imaging_to_diagnosis(self, imaging_findings: Dict[str, Any], template_id: str) -> Dict[str, Any]:
        """
        影像→诊断工作流

        Args:
            imaging_findings: 影像发现
            template_id: 模版ID

        Returns:
            诊断建议
        """
        template = self.report_templates.get(template_id)
        if not template:
            return {"error": "模版不存在"}

        try:
            # 构建推理提示词
            findings_text = self._format_findings(imaging_findings)

            prompt = f"""
            基于以下影像发现，请推荐可能的诊断：

            影像发现：
            {findings_text}

            请按以下JSON格式返回诊断建议：
            {{
              "primary_diagnosis": {{
                "name": "主要诊断",
                "confidence": 0.0-1.0,
                "reasoning": "推理依据"
              }},
              "differential_diagnoses": [
                {{
                  "name": "鉴别诊断1",
                  "confidence": 0.0-1.0,
                  "reasoning": "推理依据"
                }}
              ],
              "recommended_actions": ["建议的后续行动"]
            }}
            """

            result = self.llm_client.extract_json(prompt)

            # 添加SNOMED CT编码（如果可用）
            if isinstance(result, dict):
                result = await self._add_snomed_codes_to_diagnoses(result)

            return result

        except Exception as e:
            logger.error(f"影像→诊断推理失败: {e}")
            return {"error": str(e)}

    def _format_findings(self, findings: Dict[str, Any]) -> str:
        """格式化影像发现"""
        formatted = []
        for key, value in findings.items():
            if value:
                formatted.append(f"{key}: {value}")
        return "\n".join(formatted)

    async def _add_snomed_codes_to_diagnoses(self, diagnosis_result: Dict[str, Any]) -> Dict[str, Any]:
        """为诊断添加SNOMED CT编码"""
        # 这里可以集成SNOMED CT服务来获取编码
        # 暂时返回原结果
        return diagnosis_result

    def diagnosis_to_description(self, diagnosis_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        诊断→描述工作流

        Args:
            diagnosis_name: 诊断名称
            parameters: 描述参数

        Returns:
            生成的影像描述
        """
        diagnosis_template = self.get_diagnosis_template(diagnosis_name)
        if not diagnosis_template:
            return {"error": f"未找到诊断模版: {diagnosis_name}"}

        try:
            # 使用模版生成描述
            descriptions = []
            for template in diagnosis_template.description_templates:
                description = self._fill_template(template, parameters)
                descriptions.append(description)

            # 生成完整的影像描述
            full_description = "。".join(descriptions) + "。"

            result = {
                "diagnosis": diagnosis_name,
                "snomed_code": diagnosis_template.snomed_code,
                "generated_description": full_description,
                "individual_descriptions": descriptions,
                "required_findings": diagnosis_template.required_findings,
                "optional_findings": diagnosis_template.optional_findings,
                "differential_diagnoses": diagnosis_template.differential_diagnoses,
                "parameters_used": parameters
            }

            return result

        except Exception as e:
            logger.error(f"诊断→描述生成失败: {e}")
            return {"error": str(e)}

    def _fill_template(self, template: str, parameters: Dict[str, Any]) -> str:
        """填充模版参数"""
        filled_template = template

        # 替换模版中的参数
        for param, value in parameters.items():
            placeholder = f"{{{param}}}"
            if placeholder in filled_template:
                filled_template = filled_template.replace(placeholder, str(value))

        # 清理未填充的参数
        filled_template = re.sub(r'\{[^}]+\}', '', filled_template)

        return filled_template.strip()

    def update_template(self, template_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新模版

        Args:
            template_id: 模版ID
            updates: 更新内容

        Returns:
            是否更新成功
        """
        if template_id not in self.report_templates:
            return False

        try:
            template = self.report_templates[template_id]

            # 更新模版属性
            if "name" in updates:
                template.name = updates["name"]
            if "fields" in updates:
                # 更新字段
                new_fields = []
                for field_data in updates["fields"]:
                    field = TemplateField(**field_data)
                    new_fields.append(field)
                template.fields = new_fields
            if "generation_rules" in updates:
                template.generation_rules.update(updates["generation_rules"])

            template.updated_at = datetime.now()
            template.version = str(float(template.version) + 0.1)

            # 保存更新
            self._save_templates()

            logger.info(f"模版 {template_id} 更新成功")
            return True

        except Exception as e:
            logger.error(f"更新模版失败: {e}")
            return False

    def create_template(self, template_data: Dict[str, Any]) -> bool:
        """
        创建新模版

        Args:
            template_data: 模版数据

        Returns:
            是否创建成功
        """
        try:
            # 创建字段
            fields = []
            for field_data in template_data.get("fields", []):
                field = TemplateField(**field_data)
                fields.append(field)

            # 创建模版
            template = ReportTemplate(
                template_id=template_data["template_id"],
                name=template_data["name"],
                category=template_data["category"],
                version="1.0",
                fields=fields,
                generation_rules=template_data.get("generation_rules", {}),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            self.report_templates[template.template_id] = template
            self._save_templates()

            logger.info(f"新模版 {template.template_id} 创建成功")
            return True

        except Exception as e:
            logger.error(f"创建模版失败: {e}")
            return False

    def delete_template(self, template_id: str) -> bool:
        """
        删除模版

        Args:
            template_id: 模版ID

        Returns:
            是否删除成功
        """
        if template_id in self.report_templates:
            del self.report_templates[template_id]
            self._save_templates()
            logger.info(f"模版 {template_id} 删除成功")
            return True
        return False

    def get_template_list(self) -> List[Dict[str, Any]]:
        """获取模版列表"""
        template_list = []
        for template in self.report_templates.values():
            template_info = {
                "template_id": template.template_id,
                "name": template.name,
                "category": template.category,
                "version": template.version,
                "field_count": len(template.fields),
                "created_at": template.created_at.isoformat(),
                "updated_at": template.updated_at.isoformat()
            }
            template_list.append(template_info)

        return template_list

    def validate_template_data(self, data: Dict[str, Any], template_id: str) -> Tuple[bool, List[str]]:
        """
        验证模版数据

        Args:
            data: 要验证的数据
            template_id: 模版ID

        Returns:
            (是否有效, 错误信息列表)
        """
        template = self.report_templates.get(template_id)
        if not template:
            return False, ["模版不存在"]

        errors = []

        # 检查必填字段
        for field in template.fields:
            if field.required and field.name not in data:
                errors.append(f"缺少必填字段: {field.name}")
            elif field.name in data:
                value = data[field.name]

                # 验证选择字段的值
                if field.field_type in ["select", "multiselect"] and field.options:
                    if field.field_type == "select" and value not in field.options:
                        errors.append(f"字段 {field.name} 的值 '{value}' 不在允许的选项中")
                    elif field.field_type == "multiselect":
                        if isinstance(value, list):
                            for v in value:
                                if v not in field.options:
                                    errors.append(f"字段 {field.name} 的值 '{v}' 不在允许的选项中")
                        else:
                            errors.append(f"字段 {field.name} 应该是列表类型")

        return len(errors) == 0, errors
