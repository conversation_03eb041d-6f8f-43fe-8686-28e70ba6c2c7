"""
知识图谱管理器
整合Neo4j和KAG框架，提供医学知识图谱的存储、查询和推理功能
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import os
import sys

# 尝试导入Neo4j驱动
try:
    from neo4j import GraphDatabase
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    GraphDatabase = None

# 尝试导入KAG框架
try:
    # 添加KAG框架路径
    kag_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '07_medicine_kag')
    sys.path.append(kag_path)
    from kag.interface import KagConfig, KagMemory
    KAG_AVAILABLE = True
except ImportError:
    KAG_AVAILABLE = False
    KagConfig = None
    KagMemory = None

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class MedicalEntity:
    """医学实体"""
    entity_id: str
    entity_type: str  # 'anatomy', 'finding', 'diagnosis', 'procedure'
    name: str
    snomed_code: Optional[str] = None
    properties: Dict[str, Any] = None
    created_at: datetime = None

@dataclass
class MedicalRelationship:
    """医学关系"""
    relationship_id: str
    source_entity_id: str
    target_entity_id: str
    relationship_type: str
    properties: Dict[str, Any] = None
    confidence: float = 1.0
    created_at: datetime = None

class KnowledgeGraphManager:
    """知识图谱管理器"""
    
    def __init__(self, neo4j_uri: str = "bolt://localhost:7687", 
                 neo4j_user: str = "neo4j", 
                 neo4j_password: str = "password",
                 kag_config_path: str = None):
        
        self.neo4j_available = NEO4J_AVAILABLE
        self.kag_available = KAG_AVAILABLE
        
        # 初始化Neo4j连接
        if self.neo4j_available:
            try:
                self.driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
                self._verify_neo4j_connection()
                logger.info("Neo4j连接成功")
            except Exception as e:
                logger.error(f"Neo4j连接失败: {e}")
                self.neo4j_available = False
                self.driver = None
        else:
            logger.warning("Neo4j驱动不可用")
            self.driver = None
        
        # 初始化KAG框架
        if self.kag_available:
            try:
                if kag_config_path and os.path.exists(kag_config_path):
                    self.kag_config = KagConfig.from_file(kag_config_path)
                else:
                    # 使用默认配置
                    self.kag_config = self._create_default_kag_config()
                
                self.kag_memory = KagMemory(self.kag_config)
                logger.info("KAG框架初始化成功")
            except Exception as e:
                logger.error(f"KAG框架初始化失败: {e}")
                self.kag_available = False
                self.kag_memory = None
        else:
            logger.warning("KAG框架不可用")
            self.kag_memory = None
        
        # 创建索引
        if self.neo4j_available:
            self._create_indexes()
    
    def _verify_neo4j_connection(self):
        """验证Neo4j连接"""
        with self.driver.session() as session:
            result = session.run("RETURN 1 as test")
            record = result.single()
            if record["test"] != 1:
                raise Exception("Neo4j连接验证失败")
    
    def _create_default_kag_config(self) -> Any:
        """创建默认KAG配置"""
        # 这里应该根据实际的KAG配置格式来创建
        # 暂时返回一个占位符
        return None
    
    def _create_indexes(self):
        """创建Neo4j索引"""
        indexes = [
            "CREATE INDEX entity_id_index IF NOT EXISTS FOR (e:MedicalEntity) ON (e.entity_id)",
            "CREATE INDEX snomed_code_index IF NOT EXISTS FOR (e:MedicalEntity) ON (e.snomed_code)",
            "CREATE INDEX entity_type_index IF NOT EXISTS FOR (e:MedicalEntity) ON (e.entity_type)",
            "CREATE INDEX relationship_type_index IF NOT EXISTS FOR ()-[r:MEDICAL_RELATION]-() ON (r.relationship_type)"
        ]
        
        with self.driver.session() as session:
            for index_query in indexes:
                try:
                    session.run(index_query)
                except Exception as e:
                    logger.warning(f"创建索引失败: {e}")
    
    def store_medical_entity(self, entity: MedicalEntity) -> bool:
        """
        存储医学实体到知识图谱
        
        Args:
            entity: 医学实体
            
        Returns:
            是否存储成功
        """
        if not self.neo4j_available:
            logger.warning("Neo4j不可用，无法存储实体")
            return False
        
        try:
            with self.driver.session() as session:
                query = """
                MERGE (e:MedicalEntity {entity_id: $entity_id})
                SET e.entity_type = $entity_type,
                    e.name = $name,
                    e.snomed_code = $snomed_code,
                    e.properties = $properties,
                    e.created_at = $created_at,
                    e.updated_at = datetime()
                RETURN e
                """
                
                result = session.run(query, {
                    "entity_id": entity.entity_id,
                    "entity_type": entity.entity_type,
                    "name": entity.name,
                    "snomed_code": entity.snomed_code,
                    "properties": json.dumps(entity.properties or {}),
                    "created_at": entity.created_at or datetime.now()
                })
                
                record = result.single()
                if record:
                    logger.debug(f"实体存储成功: {entity.entity_id}")
                    return True
                else:
                    logger.error(f"实体存储失败: {entity.entity_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"存储实体失败: {e}")
            return False
    
    def store_medical_relationship(self, relationship: MedicalRelationship) -> bool:
        """
        存储医学关系到知识图谱
        
        Args:
            relationship: 医学关系
            
        Returns:
            是否存储成功
        """
        if not self.neo4j_available:
            logger.warning("Neo4j不可用，无法存储关系")
            return False
        
        try:
            with self.driver.session() as session:
                query = """
                MATCH (source:MedicalEntity {entity_id: $source_id})
                MATCH (target:MedicalEntity {entity_id: $target_id})
                MERGE (source)-[r:MEDICAL_RELATION {relationship_id: $relationship_id}]->(target)
                SET r.relationship_type = $relationship_type,
                    r.properties = $properties,
                    r.confidence = $confidence,
                    r.created_at = $created_at,
                    r.updated_at = datetime()
                RETURN r
                """
                
                result = session.run(query, {
                    "source_id": relationship.source_entity_id,
                    "target_id": relationship.target_entity_id,
                    "relationship_id": relationship.relationship_id,
                    "relationship_type": relationship.relationship_type,
                    "properties": json.dumps(relationship.properties or {}),
                    "confidence": relationship.confidence,
                    "created_at": relationship.created_at or datetime.now()
                })
                
                record = result.single()
                if record:
                    logger.debug(f"关系存储成功: {relationship.relationship_id}")
                    return True
                else:
                    logger.error(f"关系存储失败: {relationship.relationship_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"存储关系失败: {e}")
            return False
    
    def query_entities_by_type(self, entity_type: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        根据类型查询实体
        
        Args:
            entity_type: 实体类型
            limit: 返回数量限制
            
        Returns:
            实体列表
        """
        if not self.neo4j_available:
            return []
        
        try:
            with self.driver.session() as session:
                query = """
                MATCH (e:MedicalEntity {entity_type: $entity_type})
                RETURN e.entity_id as entity_id,
                       e.name as name,
                       e.snomed_code as snomed_code,
                       e.properties as properties
                LIMIT $limit
                """
                
                result = session.run(query, {
                    "entity_type": entity_type,
                    "limit": limit
                })
                
                entities = []
                for record in result:
                    entity_data = {
                        "entity_id": record["entity_id"],
                        "name": record["name"],
                        "snomed_code": record["snomed_code"],
                        "properties": json.loads(record["properties"] or "{}")
                    }
                    entities.append(entity_data)
                
                return entities
                
        except Exception as e:
            logger.error(f"查询实体失败: {e}")
            return []
    
    def query_entity_relationships(self, entity_id: str) -> List[Dict[str, Any]]:
        """
        查询实体的关系
        
        Args:
            entity_id: 实体ID
            
        Returns:
            关系列表
        """
        if not self.neo4j_available:
            return []
        
        try:
            with self.driver.session() as session:
                query = """
                MATCH (source:MedicalEntity {entity_id: $entity_id})-[r:MEDICAL_RELATION]->(target:MedicalEntity)
                RETURN r.relationship_id as relationship_id,
                       r.relationship_type as relationship_type,
                       r.confidence as confidence,
                       target.entity_id as target_entity_id,
                       target.name as target_name,
                       target.entity_type as target_type
                UNION
                MATCH (source:MedicalEntity)-[r:MEDICAL_RELATION]->(target:MedicalEntity {entity_id: $entity_id})
                RETURN r.relationship_id as relationship_id,
                       r.relationship_type as relationship_type,
                       r.confidence as confidence,
                       source.entity_id as target_entity_id,
                       source.name as target_name,
                       source.entity_type as target_type
                """
                
                result = session.run(query, {"entity_id": entity_id})
                
                relationships = []
                for record in result:
                    relationship_data = {
                        "relationship_id": record["relationship_id"],
                        "relationship_type": record["relationship_type"],
                        "confidence": record["confidence"],
                        "related_entity": {
                            "entity_id": record["target_entity_id"],
                            "name": record["target_name"],
                            "entity_type": record["target_type"]
                        }
                    }
                    relationships.append(relationship_data)
                
                return relationships
                
        except Exception as e:
            logger.error(f"查询实体关系失败: {e}")
            return []
    
    def find_diagnostic_path(self, findings: List[str], target_diagnosis: str = None) -> List[Dict[str, Any]]:
        """
        查找诊断路径
        
        Args:
            findings: 发现列表
            target_diagnosis: 目标诊断（可选）
            
        Returns:
            诊断路径列表
        """
        if not self.neo4j_available:
            return []
        
        try:
            with self.driver.session() as session:
                # 构建查询，查找从发现到诊断的路径
                if target_diagnosis:
                    query = """
                    MATCH path = (finding:MedicalEntity)-[*1..3]->(diagnosis:MedicalEntity)
                    WHERE finding.name IN $findings 
                      AND diagnosis.name = $target_diagnosis
                      AND diagnosis.entity_type = 'diagnosis'
                    RETURN path, length(path) as path_length
                    ORDER BY path_length
                    LIMIT 10
                    """
                    params = {"findings": findings, "target_diagnosis": target_diagnosis}
                else:
                    query = """
                    MATCH path = (finding:MedicalEntity)-[*1..3]->(diagnosis:MedicalEntity)
                    WHERE finding.name IN $findings 
                      AND diagnosis.entity_type = 'diagnosis'
                    RETURN path, length(path) as path_length
                    ORDER BY path_length
                    LIMIT 10
                    """
                    params = {"findings": findings}
                
                result = session.run(query, params)
                
                paths = []
                for record in result:
                    path_data = {
                        "path_length": record["path_length"],
                        "nodes": [],
                        "relationships": []
                    }
                    
                    # 解析路径中的节点和关系
                    path = record["path"]
                    for node in path.nodes:
                        node_data = {
                            "entity_id": node.get("entity_id"),
                            "name": node.get("name"),
                            "entity_type": node.get("entity_type"),
                            "snomed_code": node.get("snomed_code")
                        }
                        path_data["nodes"].append(node_data)
                    
                    for rel in path.relationships:
                        rel_data = {
                            "relationship_type": rel.get("relationship_type"),
                            "confidence": rel.get("confidence", 1.0)
                        }
                        path_data["relationships"].append(rel_data)
                    
                    paths.append(path_data)
                
                return paths
                
        except Exception as e:
            logger.error(f"查找诊断路径失败: {e}")
            return []
    
    def close(self):
        """关闭连接"""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j连接已关闭")
