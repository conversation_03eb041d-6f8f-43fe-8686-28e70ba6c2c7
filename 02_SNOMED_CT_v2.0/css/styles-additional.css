/* 
 * SNOMED CT医学影像与诊断报告结构化网站
 * 附加样式表 - 用于非首页页面的特殊样式
 */

/* 页面导航栏相关样式 */
.page-nav {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.page-nav h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.page-nav ul {
    list-style: none;
    padding: 0;
}

.page-nav li {
    margin-bottom: 10px;
}

.page-nav a {
    display: block;
    padding: 8px 12px;
    color: var(--text-color);
    border-radius: 4px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.page-nav a:hover {
    background-color: rgba(44, 109, 172, 0.05);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.page-nav a.active {
    background-color: rgba(44, 109, 172, 0.1);
    color: var(--primary-color);
    font-weight: 500;
    border-left-color: var(--primary-color);
}

/* 内容布局 */
.two-column-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

.content-section {
    flex: 1;
}

.content-block {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.content-block h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.content-block h3 {
    color: var(--primary-color);
    margin: 25px 0 15px;
    font-size: 1.3rem;
}

.content-block p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.content-block ul {
    margin: 15px 0;
    padding-left: 20px;
}

.content-block li {
    margin-bottom: 8px;
    line-height: 1.6;
}

/* 页面头部 */
.page-header {
    background-color: var(--primary-color);
    color: white;
    padding: 100px 0 50px;
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.page-header p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto;
    opacity: 0.9;
}

/* 信息框样式 */
.info-box {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    margin: 25px 0;
}

.info-box.warning {
    background-color: #fff8e6;
    border-left-color: var(--warning-color);
}

.info-box.error {
    background-color: #feecec;
    border-left-color: var(--error-color);
}

.info-box.success {
    background-color: #e6f7ed;
    border-left-color: var(--success-color);
}

.info-box h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
}

.info-box.warning h3 {
    color: #b36900;
}

.info-box.error h3 {
    color: #c92a2a;
}

.info-box.success h3 {
    color: #2b8a3e;
}

/* 组件卡片网格 */
.component-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.component-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.component-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.component-card h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid rgba(44, 109, 172, 0.2);
    padding-bottom: 8px;
}

/* 概念层次结构树 */
.hierarchy-tree {
    margin: 20px 0;
    padding-left: 15px;
}

.hierarchy-tree ul {
    padding-left: 25px;
    margin: 8px 0;
    border-left: 1px dashed var(--border-color);
}

.hierarchy-tree li {
    margin-bottom: 8px;
    position: relative;
}

.tree-item {
    display: inline-block;
    padding: 5px 12px;
    background-color: rgba(44, 109, 172, 0.08);
    border-radius: 4px;
    color: var(--primary-color);
    font-weight: 500;
}

/* 编码示例 */
.coding-example {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 25px 0;
}

.coding-example h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.coding-example pre {
    background-color: #2d3748;
    color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    line-height: 1.5;
}

/* 图片样式 */
.image-box {
    margin: 25px 0;
    text-align: center;
}

.content-image {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-caption {
    margin-top: 10px;
    color: var(--light-text);
    font-style: italic;
}

/* 应用领域网格 */
.application-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.application-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.application-card h3 {
    color: var(--primary-color);
    margin-top: 0;
    font-size: 1.2rem;
    border-bottom: 2px solid rgba(44, 109, 172, 0.2);
    padding-bottom: 8px;
    margin-bottom: 15px;
}

/* 标准集成盒子 */
.standards-integration {
    margin: 25px 0;
}

.standard-box {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.standard-box h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid rgba(44, 109, 172, 0.2);
    padding-bottom: 8px;
}

/* 优势条目 */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.benefit-item {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.benefit-item h3 {
    color: var(--primary-color);
    margin-top: 0;
    font-size: 1.2rem;
    border-bottom: 2px solid rgba(44, 109, 172, 0.2);
    padding-bottom: 8px;
    margin-bottom: 15px;
}

/* 案例卡片 */
.case-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.case-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.case-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.case-content {
    padding: 20px;
}

.case-content h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.case-content p {
    margin-bottom: 15px;
}

/* 比较布局 */
.comparison-box {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.comparison-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.comparison-item h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.comparison-item ul {
    padding-left: 20px;
}

/* 报告元素网格 */
.elements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.element-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.element-card h3 {
    color: var(--primary-color);
    margin-top: 0;
    font-size: 1.2rem;
    border-bottom: 2px solid rgba(44, 109, 172, 0.2);
    padding-bottom: 8px;
    margin-bottom: 15px;
}

/* 实现步骤 */
.process-steps {
    margin: 25px 0;
}

.step-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px 20px 20px 80px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
}

.step-number {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.step-card h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 10px;
}

/* 技术架构组件 */
.architecture-components {
    margin: 25px 0;
}

.architecture-components h3 {
    color: var(--primary-color);
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.architecture-components ul {
    padding-left: 25px;
}

.architecture-components li {
    margin-bottom: 10px;
}

/* 工作流程图 */
.workflow-diagram {
    margin: 25px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.workflow-steps {
    counter-reset: workflow-counter;
    list-style-type: none;
    padding: 0;
}

.workflow-steps li {
    position: relative;
    padding: 15px 20px 15px 60px;
    background-color: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.workflow-steps li:before {
    counter-increment: workflow-counter;
    content: counter(workflow-counter);
    position: absolute;
    left: 15px;
    top: 15px;
    width: 30px;
    height: 30px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

.workflow-step-content h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: 10px;
}

/* 挑战和解决方案 */
.challenges-solutions {
    margin: 25px 0;
}

.challenge-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.challenge-item h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.challenge-description p {
    margin-bottom: 10px;
}

/* 交互式案例演示 */
.demo-tabs {
    margin: 25px 0;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    background-color: rgba(44, 109, 172, 0.05);
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: white;
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

.case-intro {
    margin-bottom: 20px;
}

.case-intro h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 10px;
}

.report-demo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.original-report, .structured-report {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.original-report h4, .structured-report h4 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.report-text p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.report-structure {
    background-color: #f8f9fa;
}

.structure-section {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px dashed var(--border-color);
}

.structure-section h5 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1rem;
}

.structure-item {
    display: flex;
    margin-bottom: 8px;
}

.item-label {
    flex: 0 0 120px;
    color: var(--light-text);
    font-weight: 500;
}

.item-value {
    flex: 1;
}

.snomedTerm {
    color: var(--primary-color);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    border-bottom: 1px dashed var(--primary-color);
}

.snomedTerm:hover {
    color: var(--secondary-color);
}

.snomedTerm:after {
    content: "🔍";
    font-size: 0.8rem;
    margin-left: 3px;
    opacity: 0.7;
}

.code-view {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.code-view h4 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.code-prompt {
    text-align: center;
    padding: 40px 20px;
    color: var(--light-text);
    font-style: italic;
}

.concept-info {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.concept-info.hidden {
    display: none;
}

.concept-basic {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--border-color);
}

.concept-id, .concept-term {
    margin-bottom: 10px;
}

.concept-term {
    color: var(--primary-color);
    font-weight: 500;
}

.concept-relationships h5 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1rem;
}

.concept-relationships ul {
    padding-left: 20px;
}

/* 工作流程演示 */
.workflow-demo {
    margin: 25px 0;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.workflow-steps {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    padding: 15px;
}

.workflow-step {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    margin-right: 15px;
    border-radius: 20px;
    cursor: pointer;
    position: relative;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.workflow-step:after {
    content: "";
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    border-top: 2px solid var(--border-color);
    border-right: 2px solid var(--border-color);
    transform: translateY(-50%) rotate(45deg);
}

.workflow-step:last-child:after {
    display: none;
}

.workflow-step.active {
    background-color: var(--primary-color);
    color: white;
}

.workflow-step.completed {
    background-color: #e6f7ed;
    color: #2b8a3e;
}

.workflow-step .step-number {
    width: 24px;
    height: 24px;
    font-size: 0.9rem;
    margin-right: 8px;
    position: static;
    background-color: white;
    color: var(--primary-color);
}

.workflow-step.active .step-number {
    background-color: white;
    color: var(--primary-color);
}

.workflow-step.completed .step-number {
    background-color: #2b8a3e;
    color: white;
}

.workflow-content {
    padding: 20px;
}

.workflow-screen {
    display: none;
}

.workflow-screen.active {
    display: block;
}

.workflow-screen h4 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 15px;
}

.workflow-description {
    margin-bottom: 20px;
}

.workflow-image {
    text-align: center;
}

.workflow-img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 991px) {
    .two-column-layout {
        grid-template-columns: 1fr;
    }
    
    .page-nav {
        position: static;
        margin-bottom: 30px;
    }
    
    .report-demo {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 767px) {
    .page-header {
        padding: 80px 0 40px;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .page-header p {
        font-size: 1rem;
    }
    
    .component-cards, 
    .application-grid, 
    .benefits-grid, 
    .elements-grid,
    .comparison-box {
        grid-template-columns: 1fr;
    }
    
    .step-card {
        padding: 15px 15px 15px 60px;
    }
    
    .workflow-steps {
        flex-direction: column;
    }
    
    .workflow-step {
        margin-bottom: 10px;
        margin-right: 0;
    }
    
    .workflow-step:after {
        display: none;
    }
}

@media (max-width: 575px) {
    .coding-example pre {
        font-size: 0.8rem;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
    }
}