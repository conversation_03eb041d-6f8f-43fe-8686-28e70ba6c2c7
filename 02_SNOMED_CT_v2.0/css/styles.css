/* 全局样式 */
:root {
    --primary-color: #2c6dac;
    --secondary-color: #4393e6;
    --accent-color: #36b0c9;
    --text-color: #333333;
    --light-text: #666666;
    --lighter-text: #999999;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --border-color: #e1e4e8;
    --shadow-color: rgba(0, 0, 0, 0.05);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--bg-light);
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 15px;
}

p {
    margin-bottom: 1rem;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.2rem;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn.primary {
    background-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background-color: var(--secondary-color);
    color: white;
}

.btn.secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn.secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 导航栏样式 */
header {
    background-color: var(--bg-white);
    box-shadow: 0 2px 4px var(--shadow-color);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.logo h1 {
    font-size: 1.8rem;
    margin-bottom: 0;
    color: var(--primary-color);
}

.logo p {
    font-size: 0.9rem;
    color: var(--light-text);
    margin-bottom: 0;
}

.nav-links {
    display: flex;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    color: var(--text-color);
    font-weight: 500;
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a.active {
    color: var(--primary-color);
}

.nav-links a.active:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
}

.mobile-menu-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 5px 0;
    transition: all 0.3s ease;
}

/* 英雄区域样式 */
.hero {
    padding: 160px 0 80px;
    background-color: var(--bg-white);
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.hero-content {
    flex: 1;
    padding-right: 40px;
}

.hero-image {
    flex: 1;
}

.image-container {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.hero h2 {
    font-size: 2.8rem;
    line-height: 1.2;
    margin-bottom: 20px;
}

.hero h2 span {
    color: var(--primary-color);
}

.hero p {
    font-size: 1.1rem;
    color: var(--light-text);
    margin-bottom: 30px;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

/* 特性区域样式 */
.features {
    background-color: var(--bg-light);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: var(--bg-white);
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--shadow-color);
}

.feature-card .icon {
    width: 60px;
    height: 60px;
    background-color: rgba(44, 109, 172, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.feature-icon {
    width: 30px;
    height: 30px;
}

.feature-card h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.feature-card p {
    color: var(--light-text);
}

/* 介绍内容区域样式 */
.intro-content {
    background-color: var(--bg-white);
}

.content-columns {
    display: flex;
    align-items: center;
    gap: 50px;
}

.text-column {
    flex: 1;
}

.image-column {
    flex: 1;
}

.text-column h2 {
    font-size: 2.2rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.text-column p {
    margin-bottom: 20px;
    color: var(--light-text);
}

.content-image {
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 应用预览区域样式 */
.application-preview {
    background-color: var(--bg-light);
}

.application-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.app-card {
    background-color: var(--bg-white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: transform 0.3s ease;
}

.app-card:hover {
    transform: translateY(-5px);
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.card-content {
    padding: 25px;
}

.card-content h3 {
    font-size: 1.4rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.card-content p {
    color: var(--light-text);
    margin-bottom: 20px;
}

.read-more {
    font-weight: 500;
    color: var(--primary-color);
    display: inline-flex;
    align-items: center;
}

.read-more:after {
    content: '→';
    margin-left: 5px;
    transition: margin-left 0.3s ease;
}

.read-more:hover:after {
    margin-left: 10px;
}

/* CTA区域样式 */
.cta-section {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-section .btn.primary {
    background-color: white;
    color: var(--primary-color);
}

.cta-section .btn.primary:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

/* 页脚样式 */
footer {
    background-color: #1c2e4a;
    color: rgba(255, 255, 255, 0.8);
    padding: 60px 0 30px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
}

.footer-logo h2 {
    color: white;
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.footer-logo p {
    opacity: 0.7;
}

.footer-links {
    display: flex;
    gap: 60px;
}

.link-group h3 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.link-group ul li {
    margin-bottom: 10px;
}

.link-group ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s ease;
}

.link-group ul li a:hover {
    color: white;
}

.copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .hero {
        padding: 140px 0 60px;
    }
    
    .hero .container {
        flex-direction: column;
    }
    
    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
        text-align: center;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .content-columns {
        flex-direction: column;
    }
    
    .text-column {
        order: 2;
    }
    
    .image-column {
        order: 1;
        margin-bottom: 30px;
    }
    
    .footer-content {
        flex-direction: column;
    }
    
    .footer-logo {
        margin-bottom: 30px;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    section {
        padding: 60px 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: var(--bg-white);
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        box-shadow: 0 5px 10px var(--shadow-color);
        transform: translateY(-150%);
        transition: transform 0.3s ease;
    }
    
    .nav-links.active {
        transform: translateY(0);
    }
    
    .nav-links li {
        margin: 15px 0;
    }
    
    .hero h2 {
        font-size: 2.2rem;
    }
}

/* 报告结构化页面特定样式 */
.structuring-process {
    background-color: var(--bg-white);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.step-card {
    background-color: var(--bg-light);
    border-radius: 8px;
    padding: 30px;
    position: relative;
}

.step-number {
    position: absolute;
    top: -20px;
    left: 30px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

.step-card h3 {
    margin-top: 15px;
    color: var(--primary-color);
}

/* 交互案例页面特定样式 */
.report-converter {
    background-color: var(--bg-white);
    padding: 50px 0;
}

.converter-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.original-report, .structured-report {
    background-color: var(--bg-light);
    border-radius: 8px;
    padding: 30px;
    min-height: 400px;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.report-content {
    line-height: 1.8;
}

.highlighted {
    background-color: rgba(44, 109, 172, 0.1);
    color: var(--primary-color);
    padding: 2px 5px;
    border-radius: 3px;
    cursor: pointer;
}

.code-tag {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 5px;
}

.structured-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--border-color);
}

.structured-item:last-child {
    border-bottom: none;
}

.item-label {
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.concept-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.concept-id {
    font-family: monospace;
    color: var(--light-text);
    font-size: 0.9rem;
    margin-left: 10px;
}

.convert-btn {
    display: block;
    margin: 30px auto;
    padding: 12px 30px;
}